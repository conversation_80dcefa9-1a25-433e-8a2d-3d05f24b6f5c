<?php
/**
 * Plugin Name: Yoast SEO REST API Support
 * Description: 为WordPress REST API添加Yoast SEO元数据支持，支持通过API设置焦点关键词和元描述
 * Version: 1.0.0
 * Author: ThinkAcademy
 * License: GPL v2 or later
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 在REST API中注册Yoast SEO字段
 */
function register_yoast_seo_rest_fields() {
    // 注册元描述字段
    register_rest_field('post', 'yoast_meta_description', array(
        'get_callback'    => function($post) {
            return get_post_meta($post['id'], '_yoast_wpseo_metadesc', true);
        },
        'update_callback' => function($value, $post) {
            if (!empty($value)) {
                update_post_meta($post->ID, '_yoast_wpseo_metadesc', sanitize_text_field($value));
                return true;
            }
            return false;
        },
        'schema' => array(
            'type'        => 'string',
            'description' => 'Yoast SEO元描述',
            'context'     => array('view', 'edit'),
        ),
    ));

    // 注册焦点关键词字段
    register_rest_field('post', 'yoast_focus_keyword', array(
        'get_callback'    => function($post) {
            return get_post_meta($post['id'], '_yoast_wpseo_focuskw', true);
        },
        'update_callback' => function($value, $post) {
            if (!empty($value)) {
                update_post_meta($post->ID, '_yoast_wpseo_focuskw', sanitize_text_field($value));
                return true;
            }
            return false;
        },
        'schema' => array(
            'type'        => 'string',
            'description' => 'Yoast SEO焦点关键词',
            'context'     => array('view', 'edit'),
        ),
    ));

    // 注册SEO标题字段
    register_rest_field('post', 'yoast_seo_title', array(
        'get_callback'    => function($post) {
            return get_post_meta($post['id'], '_yoast_wpseo_title', true);
        },
        'update_callback' => function($value, $post) {
            if (!empty($value)) {
                update_post_meta($post->ID, '_yoast_wpseo_title', sanitize_text_field($value));
                return true;
            }
            return false;
        },
        'schema' => array(
            'type'        => 'string',
            'description' => 'Yoast SEO标题',
            'context'     => array('view', 'edit'),
        ),
    ));
}

// 在REST API初始化时注册字段
add_action('rest_api_init', 'register_yoast_seo_rest_fields');

/**
 * 添加管理员通知
 */
function yoast_rest_api_admin_notice() {
    if (current_user_can('manage_options')) {
        echo '<div class="notice notice-success is-dismissible">';
        echo '<p><strong>Yoast SEO REST API Support</strong> 插件已激活！现在可以通过REST API设置Yoast SEO元数据了。</p>';
        echo '</div>';
    }
}

// 插件激活时显示通知
register_activation_hook(__FILE__, function() {
    add_action('admin_notices', 'yoast_rest_api_admin_notice');
});

/**
 * 插件信息和使用说明
 */
function yoast_rest_api_plugin_info() {
    ?>
    <div class="wrap">
        <h1>Yoast SEO REST API Support</h1>
        <div class="card">
            <h2>使用方法</h2>
            <p>此插件为WordPress REST API添加了以下Yoast SEO字段：</p>
            <ul>
                <li><code>yoast_meta_description</code> - 元描述</li>
                <li><code>yoast_focus_keyword</code> - 焦点关键词</li>
                <li><code>yoast_seo_title</code> - SEO标题</li>
            </ul>
            
            <h3>API使用示例</h3>
            <pre><code>
// 更新文章的Yoast SEO数据
PUT /wp-json/wp/v2/posts/123
{
    "yoast_meta_description": "这是元描述",
    "yoast_focus_keyword": "关键词",
    "yoast_seo_title": "SEO标题"
}
            </code></pre>
        </div>
    </div>
    <?php
}

// 添加管理菜单
add_action('admin_menu', function() {
    add_options_page(
        'Yoast REST API',
        'Yoast REST API',
        'manage_options',
        'yoast-rest-api',
        'yoast_rest_api_plugin_info'
    );
});
