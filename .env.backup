# 服务器配置
PORT=3000
HOST=localhost

# AI模型配置（豆包Doubao）
DOUBAO_API_KEY=3add6956-51e3-434e-88da-0d814f2c66ac
DOUBAO_API_URL=https://ark.cn-beijing.volces.com/api/v3/chat/completions
DOUBAO_MODEL=doubao-seed-1-6-flash-250715
AI_MAX_TOKENS=12000
AI_TEMPERATURE=0.7

# WordPress配置
WORDPRESS_SITE_URL=https://blog-admin.thethinkacademy.com
WORDPRESS_USERNAME=thinkacademy
WORDPRESS_APP_PASSWORD=fCst zxBp gLWt PHNk Av3b 6Jrl

# 图片服务API Keys
OPENAI_API_KEY=your_openai_key_here
STABILITY_API_KEY=sk-RcQU8rWPoBpPAuuACvGd5MR6VQm79lcc8sY2I23gRCop61ED
PIXABAY_API_KEY=**********************************
PEXELS_API_KEY=1QM7QAXalYcNNneckHVjz4LZH5OyyofvCHwFgSmGue8nfxJDAB9f0OnQ
#UNSPLASH_ACCESS_KEY=*******************************************

# AWS S3配置（图片上传）
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=Eq8XWHCEf/ESUs/WGDBtuapkIKPLLMPn0feR2rVN
AWS_REGION=us-west-2
S3_BUCKET_NAME=pa-s3-prod

# 文章生成配置
ARTICLE_LANGUAGE=zh-CN
ARTICLE_MIN_LENGTH=500
ARTICLE_MAX_LENGTH=2000
INCLUDE_IMAGES=false

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log
