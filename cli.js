#!/usr/bin/env node

const readline = require('readline');
const { generateArticle } = require('./src/services/aiService');
const {
  publishArticle,
  getSiteInfo,
} = require('./src/services/wordpressService');
const { validateConfig } = require('./src/config/config');
const logger = require('./src/config/logger');

// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// 提示用户输入
function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

// 显示菜单
function showMenu() {
  console.log('\n=== AI文章生成器 - WordPress自动发布 ===');
  console.log('1. 生成文章');
  console.log('2. 一键生成并发布');
  console.log('3. 检查WordPress连接');
  console.log('4. 退出');
  console.log('=====================================');
}

// 获取文章生成选项
async function getArticleOptions() {
  console.log('\n--- 文章生成选项 ---');

  const language = (await question('语言 (zh-CN/en-US) [zh-CN]: ')) || 'zh-CN';
  const tone =
    (await question('风格 (专业/轻松/幽默/严肃) [专业]: ')) || '专业';
  const lengthChoice =
    (await question(
      '长度 (1:短文500-800字, 2:中等800-1500字, 3:长文1500-2500字) [2]: '
    )) || '2';
  const includeHeadings = (await question('包含标题结构? (y/n) [y]: ')) || 'y';

  let minLength, maxLength;
  switch (lengthChoice) {
    case '1':
      minLength = 500;
      maxLength = 800;
      break;
    case '3':
      minLength = 1500;
      maxLength = 2500;
      break;
    case '2':
    default:
      minLength = 800;
      maxLength = 1500;
  }

  return {
    language,
    tone,
    minLength,
    maxLength,
    includeHeadings: includeHeadings.toLowerCase() === 'y',
  };
}

// 生成文章
async function handleGenerateArticle() {
  try {
    const keyword = await question('\n请输入关键词: ');
    if (!keyword.trim()) {
      console.log('关键词不能为空！');
      return;
    }

    const options = await getArticleOptions();

    console.log('\n正在生成文章，请稍候...');
    const article = await generateArticle(keyword, options);

    console.log('\n=== 文章生成成功 ===');
    console.log(`标题: ${article.title}`);
    console.log(`摘要: ${article.excerpt}`);
    console.log(`分类: ${article.categories?.join(', ') || '无'}`);
    console.log(`标签: ${article.tags?.join(', ') || '无'}`);
    console.log('\n--- 文章内容 ---');
    console.log(article.content);
    console.log('--- 内容结束 ---\n');

    const shouldPublish = await question('是否发布到WordPress? (y/n): ');
    if (shouldPublish.toLowerCase() === 'y') {
      await handlePublishArticle(article);
    }
  } catch (error) {
    console.error(`生成文章失败: ${error.message}`);
    logger.error(`CLI生成文章失败: ${error.message}`, { error });
  }
}

// 发布文章
async function handlePublishArticle(article) {
  try {
    const status =
      (await question('发布状态 (draft/publish/pending) [draft]: ')) || 'draft';

    const publishData = {
      title: article.title,
      content: article.content,
      excerpt: article.excerpt,
      categories: article.categories,
      tags: article.tags,
      status,
    };

    console.log('\n正在发布文章到WordPress...');
    const result = await publishArticle(publishData);

    console.log('\n=== 文章发布成功 ===');
    console.log(`文章ID: ${result.postId}`);
    console.log(`状态: ${result.status}`);
    if (result.postUrl) {
      console.log(`链接: ${result.postUrl}`);
    }
  } catch (error) {
    console.error(`发布文章失败: ${error.message}`);
    logger.error(`CLI发布文章失败: ${error.message}`, { error });
  }
}

// 一键生成并发布
async function handleGenerateAndPublish() {
  try {
    const keyword = await question('\n请输入关键词: ');
    if (!keyword.trim()) {
      console.log('关键词不能为空！');
      return;
    }

    const options = await getArticleOptions();
    const status =
      (await question('\n发布状态 (draft/publish/pending) [draft]: ')) ||
      'draft';

    console.log('\n正在生成文章...');
    const article = await generateArticle(keyword, options);

    console.log('文章生成成功，正在发布到WordPress...');
    const publishData = {
      title: article.title,
      content: article.content,
      excerpt: article.excerpt,
      categories: article.categories,
      tags: article.tags,
      status,
    };

    const result = await publishArticle(publishData);

    console.log('\n=== 一键生成并发布成功 ===');
    console.log(`标题: ${article.title}`);
    console.log(`文章ID: ${result.postId}`);
    console.log(`状态: ${result.status}`);
    if (result.postUrl) {
      console.log(`链接: ${result.postUrl}`);
    }
  } catch (error) {
    console.error(`一键生成并发布失败: ${error.message}`);
    logger.error(`CLI一键生成并发布失败: ${error.message}`, { error });
  }
}

// 检查WordPress连接
async function handleCheckConnection() {
  try {
    console.log('\n正在检查WordPress连接...');
    const siteInfo = await getSiteInfo();

    console.log('\n=== WordPress连接成功 ===');
    console.log(`站点名称: ${siteInfo.name}`);
    console.log(`站点描述: ${siteInfo.description || '无'}`);
    console.log(`站点URL: ${siteInfo.url}`);
  } catch (error) {
    console.error(`WordPress连接失败: ${error.message}`);
    logger.error(`CLI检查WordPress连接失败: ${error.message}`, { error });
  }
}

// 主程序
async function main() {
  try {
    // 验证配置
    validateConfig();

    console.log('欢迎使用AI文章生成器！');

    while (true) {
      showMenu();
      const choice = await question('请选择操作 (1-4): ');

      switch (choice) {
        case '1':
          await handleGenerateArticle();
          break;
        case '2':
          await handleGenerateAndPublish();
          break;
        case '3':
          await handleCheckConnection();
          break;
        case '4':
          console.log('再见！');
          rl.close();
          return;
        default:
          console.log('无效选择，请重新输入。');
      }
    }
  } catch (error) {
    console.error(`程序启动失败: ${error.message}`);
    console.error('请检查配置文件和环境变量。');
    rl.close();
    process.exit(1);
  }
}

// 启动程序
if (require.main === module) {
  main();
}
