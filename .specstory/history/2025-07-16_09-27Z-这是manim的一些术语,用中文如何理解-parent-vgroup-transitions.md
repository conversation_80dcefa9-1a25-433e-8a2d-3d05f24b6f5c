<!-- Generated by SpecStory -->

# 这是manim的一些术语，用中文如何理解- **Parent VGroup transitions  (2025-07-16 09:27Z)

_**User**_

这是manim的一些术语，用中文如何理解- **Parent VGroup transitions (if applicable):**
        - If VGroups are used, specify transitions (\`Shift\`, \`Transform\`, \`FadeIn\`, \`FadeOut\`) with \`Animation\` type, direction, magnitude, target VGroup, and \`run_time\`.

---

_**Assistant**_

这些是Manim（数学动画引擎）中的术语，我来为您解释一下中文含义：

## **Parent VGroup transitions (if applicable):**
**父级VGroup过渡效果（如果适用）：**

- **VGroup**: 在Manim中是"视觉组"（Visual Group）的缩写，用于将多个几何对象或文本对象组合在一起作为一个整体进行操作。

- **Parent VGroup**: 父级VGroup，指包含其他子对象或子组的顶层组合对象。

## **过渡效果类型：**

- **`Shift`**: **位移动画** - 将对象从一个位置平移到另一个位置
- **`Transform`**: **变换动画** - 将一个对象变形为另一个对象
- **`FadeIn`**: **淡入动画** - 对象逐渐显现
- **`FadeOut`**: **淡出动画** - 对象逐渐消失

## **动画参数：**

- **`Animation` type**: **动画类型** - 指定使用哪种动画效果
- **direction**: **方向** - 动画的移动方向（如上、下、左、右）
- **magnitude**: **幅度/强度** - 动画效果的大小或强度
- **target VGroup**: **目标VGroup** - 动画作用的目标视觉组
- **`run_time`**: **运行时间** - 动画播放的持续时间（秒）

## **整体含义：**
这段描述的是在Manim中，当使用VGroup（视觉组）时，如何为父级组指定过渡动画效果。需要明确动画类型、方向、强度、目标对象和播放时长等参数，以创建流畅的数学动画演示。

这通常用于制作教育视频中的数学公式变换、几何图形移动等动画效果。

---

_**User**_

- **Parent VGroup transitions (if applicable):**
        - If VGroups are used, specify transitions (\`Shift\`, \`Transform\`, \`FadeIn\`, \`FadeOut\`) with \`Animation\` type, direction, magnitude, target VGroup, and \`run_time\`.
        - **Explain the pedagogical rationale** for each VGroup transition. How does it guide the viewer's attention or contribute to understanding the scene's learning objectives? Ensure spatial coherence and respect for constraints.
    - **Element animations within VGroups and for individual Mobjects:**
        - Specify animation types (\`Create\`, \`Write\`, \`FadeIn\`, \`Transform\`, \`Circumscribe\`, \`AnimationGroup\`, \`Succession\`) for elements.
        - For each element animation, specify \`Animation\` type, target object(s), and \`run_time\`. Detail sequences and timing for \`AnimationGroup\` or \`Succession\`.
        - **Explain the pedagogical purpose** of each element animation. How does it break down complex information, highlight key details, or improve visual clarity for learning? Ensure spatial coherence and minimum spacing.
        - **Coordinate element animations with VGroup transitions:**
            - Clearly describe the synchronization between element animations and VGroup transitions (if any).
            - Specify relative timing and \`run_time\` to illustrate coordination.
            - **Explain how this animation sequence and coordination creates a pedagogical flow**, guiding the viewer's eye and attention logically through the learning material.
这是manim相关的提示词，转成中文的提示词是什么

---

_**Assistant**_

以下是将这些Manim相关提示词转换为中文的版本：

```
- **父级VGroup过渡效果（如果适用）：**
    - 如果使用了VGroup，请指定过渡效果（`Shift`（位移）、`Transform`（变换）、`FadeIn`（淡入）、`FadeOut`（淡出）），包括`Animation`动画类型、方向、幅度、目标VGroup和`run_time`运行时间。
    - **解释每个VGroup过渡效果的教学理念**。它如何引导观看者的注意力或有助于理解场景的学习目标？确保空间连贯性并遵守约束条件。

- **VGroup内部元素动画和单个Mobject对象的动画：**
    - 为元素指定动画类型（`Create`（创建）、`Write`（书写）、`FadeIn`（淡入）、`Transform`（变换）、`Circumscribe`（环绕）、`AnimationGroup`（动画组）、`Succession`（连续动画））。
    - 对于每个元素动画，指定`Animation`动画类型、目标对象和`run_time`运行时间。详细说明`AnimationGroup`或`Succession`的序列和时间安排。
    - **解释每个元素动画的教学目的**。它如何分解复杂信息、突出关键细节，或提高学习的视觉清晰度？确保空间连贯性和最小间距。
    - **协调元素动画与VGroup过渡效果：**
        - 清楚描述元素动画与VGroup过渡效果之间的同步关系（如果有的话）。
        - 指定相对时间和`run_time`运行时间以说明协调性。
        - **解释这种动画序列和协调如何创建教学流程**，逻辑性地引导观看者的视线和注意力贯穿学习材料。
```

## 关键术语对照表：

| 英文术语 | 中文翻译 | 说明 |
|---------|---------|------|
| Parent VGroup | 父级VGroup | 包含子元素的顶层视觉组 |
| transitions | 过渡效果 | 动画转换效果 |
| Shift | 位移 | 平移动画 |
| Transform | 变换 | 形态转换动画 |
| FadeIn | 淡入 | 渐显动画 |
| FadeOut | 淡出 | 渐隐动画 |
| Animation type | 动画类型 | 动画效果的种类 |
| run_time | 运行时间 | 动画播放时长 |
| pedagogical rationale | 教学理念 | 教育学原理 |
| Mobjects | Mobject对象 | Manim中的数学对象 |
| Create | 创建 | 对象创建动画 |
| Write | 书写 | 文字书写动画 |
| Circumscribe | 环绕 | 环绕突出动画 |
| AnimationGroup | 动画组 | 并行动画组合 |
| Succession | 连续动画 | 顺序动画序列 |
| spatial coherence | 空间连贯性 | 空间布局的逻辑性 |
| pedagogical flow | 教学流程 | 教育引导流程 |

这些提示词主要用于指导如何在Manim中创建具有教育意义的数学动画，强调动画效果要服务于教学目标。

---

_**User**_

2. **Scene Flow - Pedagogical Pacing and Clarity:** Detail the overall flow of the scene, emphasizing pedagogical effectiveness.
    - **Overall animation sequence, spatial progression for learning:**
        - Describe the complete animation sequence, broken down into pedagogical sub-sections (e.g., "Introducing the Problem", "Step-by-step Solution", "Concept Reinforcement").
        - Outline the spatial progression of objects and VGroups, focusing on how it supports the **pedagogical narrative** and concept development.
        - Ensure a clear and logical visual flow optimized for learning, respecting spatial constraints.
    - **Transition buffers for pedagogical pauses:**
        - Specify \`Wait()\` times between animation sections for visual separation and **learner processing time**.
        - For each \`Wait()\`, specify duration and **explain the pedagogical reason** for this buffer (e.g., "Allow viewers time to process the formula", "Create a pause for reflection before moving to the next concept").
    - **Coordinate animation timing with narration for engagement and comprehension:**
        - Describe how animation timings are coordinated with the narration script to **maximize viewer engagement and comprehension**.
        - Specify animation cues within the narration script and explain how these cues are synchronized with animations to **reinforce learning points** at the optimal moment.
这个也转一下

---

_**Assistant**_

以下是将这段Manim相关提示词转换为中文的版本：

```
2. **场景流程 - 教学节奏和清晰度：** 详细说明场景的整体流程，强调教学效果。
    - **整体动画序列，学习的空间推进：**
        - 描述完整的动画序列，分解为教学子部分（例如："问题介绍"、"逐步解决方案"、"概念强化"）。
        - 概述对象和VGroup的空间推进，重点关注它如何支持**教学叙事**和概念发展。
        - 确保清晰且合乎逻辑的视觉流程，针对学习进行优化，遵守空间约束。
    - **教学暂停的过渡缓冲：**
        - 指定动画部分之间的`Wait()`等待时间，用于视觉分离和**学习者处理时间**。
        - 对于每个`Wait()`，指定持续时间并**解释教学原因**（例如："让观看者有时间处理公式"、"在进入下一个概念前创建反思暂停"）。
    - **协调动画时间与解说以提高参与度和理解力：**
        - 描述动画时间如何与解说脚本协调，以**最大化观看者参与度和理解力**。
        - 在解说脚本中指定动画提示，并解释这些提示如何与动画同步，以在最佳时刻**强化学习要点**。
```

## 关键术语对照表：

| 英文术语 | 中文翻译 | 说明 |
|---------|---------|------|
| Scene Flow | 场景流程 | 整个动画场景的进行流程 |
| Pedagogical Pacing | 教学节奏 | 教学内容的时间安排和节奏控制 |
| Pedagogical Clarity | 教学清晰度 | 教学内容的清晰明了程度 |
| Overall animation sequence | 整体动画序列 | 完整的动画播放顺序 |
| Spatial progression | 空间推进 | 对象在空间中的移动和发展 |
| Pedagogical sub-sections | 教学子部分 | 教学内容的分段 |
| Pedagogical narrative | 教学叙事 | 教学故事线和逻辑发展 |
| Concept development | 概念发展 | 概念的逐步建立过程 |
| Transition buffers | 过渡缓冲 | 动画间的过渡时间 |
| Learner processing time | 学习者处理时间 | 学习者理解和消化内容的时间 |
| Wait() | Wait()等待 | Manim中的暂停函数 |
| Animation cues | 动画提示 | 动画开始的信号或标记 |
| Narration script | 解说脚本 | 旁白解说文本 |
| Viewer engagement | 观看者参与度 | 观众的注意力和投入程度 |
| Comprehension | 理解力 | 理解和掌握程度 |
| Learning points | 学习要点 | 重要的学习内容 |

这些提示词主要用于指导如何在Manim中创建结构化、有教学意义的数学动画，特别强调动画的教学效果和学习者的认知过程。

---

_**User**_

- **Pedagogical Narration Script:**
    - Provide the full narration script for Scene {scene_number}.
    - **Embed precise animation timing cues** within the narration script (as described before).
    - **The script should be written as if delivered by a knowledgeable and engaging lecturer.** It should:
        - **Clearly explain concepts step-by-step.**
        - **Use analogies and real-world examples to enhance understanding.**
        - **Pose questions to encourage active thinking.**
        - **Summarize key points and transitions.**
        - **Be detailed and knowledge-rich, not just visually descriptive.**
        - **Connect smoothly with the previous and subsequent scenes, acting as a segment within a single, cohesive video. 
        - Avoid repetitive introductions or conclusions.** 
        - Consider using phrases like "Building on what we saw in the previous part..." or "Let's now move on to..." to create a sense of continuity.
        - Reference the scene number when appropriate (e.g., "Now, let's explore...").
    - **Crucially, the narration should seamlessly integrate with the animations to create a cohesive and effective learning experience.**
- **Narration Sync - Pedagogical Alignment:**
    - Detail the synchronization strategy between narration and animations, emphasizing **pedagogical alignment**.
    - Explain how narration timing is aligned with animation start/end times to **guide viewer attention to key learning elements precisely when they animate.**
    - Emphasize how narration cues and animation timings work together to **create a synchronized audiovisual presentation that maximizes learning and retention.**
这个也转一下

---

_**Assistant**_

以下是将这段Manim相关提示词转换为中文的版本：

```
- **教学解说脚本：**
    - 提供场景{scene_number}的完整解说脚本。
    - **在解说脚本中嵌入精确的动画时间提示**（如前所述）。
    - **脚本应该像一位知识渊博且富有吸引力的讲师在讲授一样。** 它应该：
        - **逐步清晰地解释概念。**
        - **使用类比和现实世界的例子来增强理解。**
        - **提出问题以鼓励主动思考。**
        - **总结关键要点和过渡。**
        - **内容详细且知识丰富，而不仅仅是视觉描述。**
        - **与前后场景顺畅连接，作为单一连贯视频中的一个片段。
        - 避免重复的开头或结尾。**
        - 考虑使用诸如"基于我们在前面部分看到的..."或"现在让我们继续..."等短语来创造连续感。
        - 在适当时引用场景编号（例如："现在，让我们探索..."）。
    - **关键是，解说应该与动画无缝集成，创造连贯且有效的学习体验。**
- **解说同步 - 教学对齐：**
    - 详述解说与动画之间的同步策略，强调**教学对齐**。
    - 解释解说时间如何与动画开始/结束时间对齐，以**在关键学习元素进行动画时精确引导观看者注意力。**
    - 强调解说提示和动画时间如何共同作用，**创造同步的视听呈现，最大化学习和记忆效果。**
```

## 关键术语对照表：

| 英文术语 | 中文翻译 | 说明 |
|---------|---------|------|
| Pedagogical Narration Script | 教学解说脚本 | 用于教学的旁白解说文本 |
| Animation timing cues | 动画时间提示 | 动画开始的时间标记 |
| Knowledgeable lecturer | 知识渊博的讲师 | 专业且有经验的教师 |
| Engaging lecturer | 富有吸引力的讲师 | 能够吸引学生注意力的教师 |
| Step-by-step | 逐步地 | 按步骤进行 |
| Analogies | 类比 | 通过相似事物进行比较说明 |
| Real-world examples | 现实世界的例子 | 实际生活中的案例 |
| Active thinking | 主动思考 | 积极的思维活动 |
| Key points | 关键要点 | 重要的学习内容 |
| Transitions | 过渡 | 内容间的衔接 |
| Knowledge-rich | 知识丰富 | 包含大量有用信息 |
| Visually descriptive | 视觉描述性 | 仅描述看到的内容 |
| Cohesive video | 连贯视频 | 逻辑一致的视频内容 |
| Continuity | 连续感 | 内容的连贯性 |
| Scene number | 场景编号 | 场景的序号标识 |
| Seamlessly integrate | 无缝集成 | 完美融合 |
| Learning experience | 学习体验 | 学习过程的感受 |
| Narration Sync | 解说同步 | 解说与其他元素的时间配合 |
| Pedagogical Alignment | 教学对齐 | 与教学目标的一致性 |
| Synchronization strategy | 同步策略 | 时间配合的方法 |
| Viewer attention | 观看者注意力 | 观众的关注焦点 |
| Learning elements | 学习元素 | 教学内容的组成部分 |
| Audiovisual presentation | 视听呈现 | 声音和视觉的结合展示 |
| Learning and retention | 学习和记忆 | 学习效果和记忆保持 |

这些提示词主要用于指导如何创建高质量的教学解说脚本，强调解说与动画的完美配合，以达到最佳的教学效果。

---

_**User**_


const _promptCodeGeneration = `You are an expert Manim (Community Edition) developer for educational content. Generate executable Manim code implementing animations as specified, *strictly adhering to the provided Manim documentation context, technical implementation plan, animation and narration plan, and all defined spatial constraints (safe area margins: 0.5 units, minimum spacing: 0.3 units)*.

Think of reusable animation components for a clean, modular, and maintainable library, *prioritizing code structure and best practices as demonstrated in the Manim documentation context*. *Throughout code generation, rigorously validate all spatial positioning and animations against the defined safe area margins and minimum spacing constraints. If any potential constraint violation is detected, generate a comment in the code highlighting the issue for manual review and correction.*

Input Context:

Topic: {topic}
Description: {description}

Scene Outline:
{scene_outline}

Scene Technical Implementation:
{scene_implementation}

**Code Generation Guidelines:**

1.  **Scene Class:** Class name \`Scene{scene_number}\`, where \`{scene_number}\` is replaced by the scene number (e.g., \`Scene1\`, \`Scene2\`). The scene class should at least inherit from \`VoiceoverScene\`. However, you can add more Manim Scene classes on top of VoiceoverScene for multiple inheritance if needed.
2.  **Imports:** Include ALL necessary imports explicitly at the top of the file, based on used Manim classes, functions, colors, and constants. Do not rely on implicit imports. Double-check for required modules, classes, functions, colors, and constants, *ensuring all imports are valid and consistent with the Manim Documentation*.  **Include imports for any used Manim plugins.**
3.  **Speech Service:** Initialize \`KokoroService()\`. You MUST import like this: \`from src.utils.kokoro_voiceover import KokoroService\` as this is our custom voiceover service.
4.  **Reusable Animations:** Implement functions for each animation sequence to create modular and reusable code. Structure code into well-defined functions, following function definition patterns from Manim Documentation.
5.  **Voiceover:** Use \`with self.voiceover(text="...")\` for speech synchronization, precisely matching the narration script and animation timings from the Animation and Narration Plan.
6.  **Comments:** Add clear and concise comments for complex animations, spatial logic (positioning, arrangements), and object lifecycle management. *Use comments extensively to explain code logic, especially for spatial positioning, animation sequences, and constraint enforcement, mirroring commenting style in Manim Documentation*.  **Add comments to explain the purpose and usage of any Manim plugins.**
7.  **Error Handling & Constraint Validation:** Implement basic error handling if error handling strategies are suggested or exemplified in the Manim Documentation. **Critically, during code generation, implement explicit checks to validate if each object's position and animation adheres to the safe area margins (0.5 units) and minimum spacing (0.3 units).**
8.  **Performance:** Follow Manim best practices for efficient code and rendering performance, as recommended in the Manim Documentation.
9.  **Manim Plugins:** You are allowed and encouraged to use established, well-documented Manim plugins if they simplify the code, improve efficiency, or provide functionality not readily available in core Manim.
    *   **If a plugin is used:**
        *   Include the necessary import statement at the top of the file.
        *   Add a comment indicating the plugin used and its purpose: \`### Plugin: <plugin_name> - <brief justification>\`.
        *   Ensure all plugin usage adheres to the plugin's documentation.
10. **No External Assets:** No external files (images, audio, video). *Use only Manim built-in elements and procedural generation, or elements provided by approved Manim plugins. No external assets are allowed*.
11. **No Main Function:** Only scene class. No \`if __name__ == "__main__":\`.
12. **Spatial Accuracy (Paramount):** Achieve accurate spatial positioning as described in the technical implementation plan, *strictly using relative positioning methods (\`next_to\`, \`align_to\`, \`shift\`, VGroups) and enforcing safe area margins and minimum 0.3 unit spacing, as documented in Manim Documentation Context*. *Spatial accuracy and constraint adherence are the highest priorities in code generation.*
13. **VGroup Structure:** Implement VGroup hierarchy precisely as defined in the Technical Implementation Plan, using documented VGroup methods for object grouping and manipulation.
14. **Spacing & Margins (Strict Enforcement):** Adhere strictly to safe area margins (0.5 units) and minimum spacing (0.3 units) requirements for *all* objects and VGroups throughout the scene and all animations. Prevent overlaps and ensure all objects stay within the safe area. *Rigorously enforce spacing and margin requirements using \`buff\` parameters, relative positioning, and explicit constraint validation checks during code generation, and validate against safe area guidelines from Manim Documentation Context*.
15. **Background:** Default background (Black) is sufficient. Do not create custom color background Rectangles.
16. **Text Color:** Do not use BLACK color for any text. Use predefined colors (BLUE_C, BLUE_D, GREEN_C, GREEN_D, GREY_A, GREY_B, GREY_C, LIGHTER_GRAY, LIGHT_GRAY, GOLD_C, GOLD_D, PURPLE_C, TEAL_C, TEAL_D, WHITE).
17. **Default Colors:** You MUST use the provided color definitions if you use colors in your code. ONLY USE THE COLORS PREVIOUSLY DEFINED.
18. **Animation Timings and Narration Sync:** Implement animations with precise \`run_time\` values and synchronize them with the narration script according to the Animation and Narration Plan. Use \`Wait()\` commands with specified durations for transition buffers.
19. **Don't be lazy on code generation:** Generate full, complete code including all helper functions. Ensure that the output is comprehensive and the code is fully functional, incorporating all necessary helper methods and complete scene implementation details.
20. **LaTeX Package Handling:** If the technical implementation plan specifies the need for additional LaTeX packages:
    *   Create a \`TexTemplate\` object.
    *   Use \`myTemplate = TexTemplate()\`
    *   Use \`myTemplate.add_to_preamble(r"\\usepackage{{package_name}}")\` to add the required package.
    *   Pass this template to the \`Tex\` or \`MathTex\` object: \`tex = Tex(..., tex_template=myTemplate)\`.

**Example Code Style and Structure to Emulate:**

*   **Helper Classes:** Utilize helper classes (like \`Scene2_Helper\`) to encapsulate object creation and scene logic, promoting modularity and reusability.
*   **Stage-Based \`construct\` Method:** Structure the \`construct\` method into logical stages (e.g., Stage 1, Stage 2, Stage 3) with comments to organize the scene flow.
*   **Reusable Object Creation Functions:** Define reusable functions within helper classes for creating specific Manim objects (e.g., \`create_axes\`, \`create_formula_tex\`, \`create_explanation_text\`).
*   **Clear Comments and Variable Names:** Use clear, concise comments to explain code sections and logic. Employ descriptive variable names (e.g., \`linear_function_formula\`, \`logistic_plot\`) for better readability.
*   **Text Elements:** Create text elements using \`Tex\` or \`MathTex\` for formulas and explanations, styling them with \`color\` and \`font_size\` as needed.
*   **Manim Best Practices:** Follow Manim best practices, including using \`VoiceoverScene\`, \`KokoroService\`, common Manim objects, animations, relative positioning, and predefined colors.

You MUST generate the Python code in the following format (from <CODE> to </CODE>):
<CODE>
\`\`\`python
from manim import *
from manim import config as global_config
from manim_voiceover import VoiceoverScene
from src.utils.kokoro_voiceover import KokoroService # You MUST import like this as this is our custom voiceover service.

# plugins imports, don't change the import statements
from manim_circuit import *
from manim_physics import *
from manim_chemistry import *
from manim_dsa import *
from manim_ml import *

# Helper Functions/Classes (Implement and use helper classes and functions for improved code reusability and organization)
class Scene{scene_number}_Helper:  # Example: class Scene1_Helper:
    # Helper class containing utility functions for scene {scene_number}.
    def __init__(self, scene):
        self.scene = scene
        # ... (add any necessary initializations)

    # Reusable object creation functions (Implement object creation functions for modularity and reusability as per plan)
    def get_center_of_edges(self, polygon, buff=SMALL_BUFF*3):
        # Calculate the center points of each edge in a polygon (Triangle, Square, etc.) with an optional buffer.
        # Get the vertices of the polygon
        vertices = polygon.get_vertices()
        n_vertices = len(vertices)
        # Initialize list to store edge centers
        coords_vertices = []
        # Calculate center point and normal for each edge
        for i in range(n_vertices):
            # Get current and next vertex (wrapping around to first vertex)
            v1 = vertices[i]
            v2 = vertices[(i + 1) % n_vertices]
            # Calculate edge center
            edge_center = (v1 + v2) / 2
            # Calculate edge vector and normalize
            edge_vector = v2 - v1
            edge_length = np.linalg.norm(edge_vector)
            normal = np.array([-edge_vector[1], edge_vector[0], 0]) / edge_length
            # Add buffer in the normal direction
            coords_vertices.append(edge_center + normal * buff)

        return coords_vertices

    def create_formula_tex(self, formula_str, color):
        # Example function to create a MathTex formula with a specified color.
        # Check if a custom TexTemplate is needed (from the technical plan).
        if hasattr(self.scene, 'tex_template'):
            formula = MathTex(formula_str, color=color, tex_template=self.scene.tex_template)
        else:
            formula = MathTex(formula_str, color=color)
        return formula

    # ... (add more helper functions as needed for object creation and scene logic)


class Scene{scene_number}(VoiceoverScene, MovingCameraScene):  # Note: You can add more Manim Scene classes on top of current templates for multiple inheritance if needed.
    # Reminder: This scene class is fully self-contained. There is no dependency on the implementation from previous or subsequent scenes.
    def construct(self):
        # Initialize speech service
        self.set_speech_service(KokoroService())

        # Instantiate helper class (as per plan)
        helper = Scene{scene_number}_Helper(self)  # Example: helper = Scene1_Helper(self)

        # Check for LaTeX packages and create TexTemplate if needed.
        # This section should be generated based on the technical implementation plan.
        # For example, if the plan includes:  "Requires: \\usepackage{{amsmath}}"
        # Then generate:
        #
        # my_template = TexTemplate()
        # my_template.add_to_preamble(r"\\usepackage{{amsmath}}")
        # self.tex_template = my_template

        # --- Stage 1: Scene Setup (adapt stage numbers and descriptions to your scene, following plan) ---
        with self.voiceover(text="[Narration for Stage 1 - from Animation and Narration Plan]") as tracker:  # Voiceover for Stage 1
            # Object Creation using helper functions (as per plan)
            axes = helper.create_axes()  # Example: axes = helper.create_axes()
            formula = helper.create_formula_tex("...", BLUE_C)  # Example: formula = helper.create_formula_tex("...", BLUE_C)
            explanation = helper.create_explanation_text("...")  # Example: explanation = helper.create_explanation_text("...")

            # Positioning objects (relative positioning, constraint validation - as per plan)
            formula.to_corner(UL)  # Example positioning
            axes.move_to(ORIGIN)  # Example positioning
            explanation.next_to(axes, RIGHT)  # Example positioning

            # Animations for Stage 1 (synced with voiceover - as per plan)
            self.play(Write(formula), Write(axes), run_time=tracker.duration)  # Example animations
            self.wait(0.5)  # Transition buffer

        # --- Stage 2:  ... (Implement Stage 2, Stage 3, etc. in a similar modular and structured way, following plan) ---
        with self.voiceover(text="[Narration for Stage 2 - from Animation and Narration Plan]") as tracker:  # Voiceover for Stage 2
            # ... (Object creation, positioning, and animations for Stage 2, using helper functions and constraint validation)
            pass  # Replace with actual Stage 2 code

        # ... (Implement remaining stages in a similar modular and structured way, following the Animation and Narration Plan and Technical Implementation Plan, and rigorously validating spatial constraints in each stage)

        self.wait(1)  # Scene end transition buffer
\`\`\`
</CODE>

Notes:
The \`get_center_of_edges\` helper function is particularly useful for:
1. Finding the midpoint of polygon edges for label placement
2. Calculating offset positions for side labels that don't overlap with the polygon
3. Creating consistent label positioning across different polygon sizes and orientations

Example usage in your scene:
\`\`\`python
def label_triangle_sides(self, triangle, labels=["a", "b", "c"]):
    # Helper function to label triangle sides.
    edge_centers = self.helper.get_center_of_edges(triangle)
    labeled_sides = VGroup()
    for center, label in zip(edge_centers, labels):
            tex = MathTex(label).move_to(center)
            labeled_sides.add(tex)
        return labeled_sides
\`\`\``; 转为中文提示词

---

_**Assistant**_

以下是将这段Manim代码生成提示词转换为中文的版本： 我注意到代码中没有找到这个常量定义。让我先查看文件的内容，看看是否存在这个常量或类似的内容： 看起来这个常量在当前文件中不存在。基于用户的请求，他们想要一个中文版本的Manim代码生成提示词。让我创建这个常量： 
```

```
我已经成功将Manim代码生成提示词转换为中文版本并添加到了`aiService.js`文件中。这个中文版本的提示词包含了：

## 主要翻译内容：

### **核心概念翻译：**
- **Scene Class** → **场景类**
- **Imports** → **导入**
- **Speech Service** → **语音服务**
- **Reusable Animations** → **可重用动画**
- **Voiceover** → **解说**
- **Error Handling & Constraint Validation** → **错误处理和约束验证**
- **Spatial Accuracy** → **空间精度**
- **VGroup Structure** → **VGroup结构**
- **Animation Timings and Narration Sync** → **动画时间和解说同步**

### **技术要求翻译：**
- **Safe area margins** → **安全区域边距**
- **Minimum spacing** → **最小间距**
- **Helper Classes** → **辅助类**
- **Stage-Based construct Method** → **基于阶段的construct方法**
- **Reusable Object Creation Functions** → **可重用对象创建函数**

### **代码结构指导：**
- 保持了所有技术细节和约束要求
- 翻译了注释和变量命名建议
- 保留了所有代码示例的结构
- 维持了原有的格式和缩进

这个中文版本的提示词可以帮助中文开发者更好地理解如何使用Manim创建教育动画，同时保持了所有技术要求和最佳实践的完整性。

---

