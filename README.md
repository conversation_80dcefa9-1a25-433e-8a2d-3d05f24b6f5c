# AI 文章生成器 - WordPress 自动发布

一个基于 Node.js 的 AI 驱动文章生成和 WordPress 自动发布工具。用户只需输入关键词，系统就会自动生成高质量文章并发布到 WordPress 站点。

## 功能特性

- 🤖 **AI 文章生成**: 基于豆包(Doubao)模型，根据关键词生成原创文章
- 📝 **智能内容结构**: 自动生成标题、摘要、分类和标签
- 🚀 **一键发布**: 直接发布到 WordPress 站点，支持草稿、发布、待审核状态
- 🎨 **友好界面**: 简洁的 Web 界面，支持实时预览
- 📊 **灵活配置**: 支持多种文章风格、长度和语言设置
- 🔒 **安全认证**: 支持 WordPress 应用密码和用户名密码认证
- 📋 **日志记录**: 完整的操作日志和错误追踪

## 技术栈

- **后端**: Node.js + Express
- **AI 服务**: 豆包(Doubao) API
- **WordPress 集成**: WordPress REST API
- **前端**: Bootstrap 5 + Vanilla JavaScript
- **日志**: Winston
- **安全**: Helmet + CORS

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

创建 `.env` 文件并填写配置：

```bash
touch .env
```

编辑 `.env` 文件：

```env
# AI模型配置（豆包Doubao）
DOUBAO_API_KEY=your_doubao_api_key_here
DOUBAO_API_URL=https://ark.cn-beijing.volces.com/api/v3/chat/completions
DOUBAO_MODEL=doubao-seed-1-6-flash-250715

# 默认AI服务
DEFAULT_AI_SERVICE=doubao

# WordPress配置
WORDPRESS_SITE_URL=https://your-wordpress-site.com
WORDPRESS_USERNAME=your_username
WORDPRESS_APP_PASSWORD=your_application_password

# 其他配置（可选）
PORT=3000
ARTICLE_LANGUAGE=zh-CN
```

### 3. 启动应用

开发模式：

```bash
npm run dev
```

生产模式：

```bash
npm start
```

### 4. 访问应用

打开浏览器访问 `http://localhost:3000`

## 配置说明

### 豆包(Doubao) 配置

请按以下步骤配置豆包 API：

1. 获取你的豆包 API 密钥
2. API 地址: `https://ark.cn-beijing.volces.com/api/v3/chat/completions`
3. 模型: `doubao-seed-1.6-250615`
4. 将密钥填入 `.env` 文件的 `AI_API_KEY` 字段

### WordPress 配置

#### 方法 1: 使用应用密码（推荐）

1. 登录 WordPress 后台
2. 进入 `用户 > 个人资料`
3. 滚动到底部的"应用密码"部分
4. 创建新的应用密码
5. 将用户名和应用密码填入环境变量

#### 方法 2: 使用用户名密码

直接使用 WordPress 登录用户名和密码（不推荐，安全性较低）

### 环境变量详解

| 变量名                   | 必需 | 默认值                 | 说明               |
| ------------------------ | ---- | ---------------------- | ------------------ |
| `DOUBAO_API_KEY`         | ✅   | -                      | 豆包 API 密钥      |
| `WORDPRESS_SITE_URL`     | ✅   | -                      | WordPress 站点 URL |
| `WORDPRESS_USERNAME`     | ✅   | -                      | WordPress 用户名   |
| `WORDPRESS_APP_PASSWORD` | ⚠️   | -                      | WordPress 应用密码 |
| `WORDPRESS_PASSWORD`     | ⚠️   | -                      | WordPress 用户密码 |
| `PORT`                   | ❌   | 3000                   | 服务器端口         |
| `AI_MODEL`               | ❌   | doubao-seed-1.6-250615 | 豆包模型           |
| `ARTICLE_LANGUAGE`       | ❌   | zh-CN                  | 默认文章语言       |

> ⚠️ `WORDPRESS_APP_PASSWORD` 和 `WORDPRESS_PASSWORD` 至少需要配置一个

## API 接口

### 生成文章

```http
POST /api/generate
Content-Type: application/json

{
  "keyword": "人工智能",
  "options": {
    "language": "zh-CN",
    "tone": "专业",
    "minLength": 800,
    "maxLength": 1500,
    "includeHeadings": true
  }
}
```

### 发布文章

```http
POST /api/publish
Content-Type: application/json

{
  "title": "文章标题",
  "content": "文章内容（Markdown格式）",
  "excerpt": "文章摘要",
  "categories": ["分类1", "分类2"],
  "tags": ["标签1", "标签2"],
  "status": "draft"
}
```

### 一键生成并发布

```http
POST /api/generate-and-publish
Content-Type: application/json

{
  "keyword": "人工智能",
  "options": {
    "language": "zh-CN",
    "tone": "专业"
  },
  "publishOptions": {
    "status": "draft"
  }
}
```

## 项目结构

```
src/
├── app.js                 # 主应用文件
├── config/
│   ├── config.js         # 配置管理
│   └── logger.js         # 日志配置
├── controllers/
│   └── articleController.js  # 文章控制器
├── services/
│   ├── aiService.js      # 豆包AI服务
│   └── wordpressService.js  # WordPress服务
├── routes/
│   └── api.js           # API路由
└── public/
    ├── index.html       # 前端页面
    └── js/
        └── main.js      # 前端JavaScript
```

## 故障排除

### 常见问题

1. **豆包 API 调用失败**

   - 检查网络连接
   - 确认 API 地址是否正确
   - 确认 API 密钥是否有效

2. **WordPress 连接失败**

   - 确认站点 URL 格式正确（包含 https://）
   - 检查用户名和密码/应用密码
   - 确认 WordPress 启用了 REST API

3. **文章发布失败**
   - 检查用户权限（需要发布文章权限）
   - 确认分类和标签名称有效
   - 检查文章内容格式

### 日志查看

应用日志保存在 `logs/` 目录下：

- `app.log`: 应用日志
- `error.log`: 错误日志
- `access.log`: HTTP 请求日志

## 开发指南

### 添加新功能

1. 在相应的 service 中添加业务逻辑
2. 在 controller 中添加请求处理
3. 在 routes 中添加路由
4. 更新前端界面（如需要）

### 测试

```bash
# 运行测试（待实现）
npm test
```

## 许可证

ISC License

## 贡献

欢迎提交 Issue 和 Pull Request！
