# 使用指南

## 快速开始

### 1. 环境准备

确保你已经安装了 Node.js (版本 14 或更高)。

### 2. 获取必要的 API 密钥和配置

#### 豆包(Doubao) API 配置

项目已预配置了豆包 API 的访问信息，无需额外配置：

1. API 密钥: `3add6956-51e3-434e-88da-0d814f2c66ac`
2. API 地址: `https://ark.cn-beijing.volces.com/api/v3/chat/completions`
3. 模型: `doubao-seed-1.6-250615`

#### WordPress 配置

1. 确保你的 WordPress 站点支持 REST API（WordPress 4.7+默认支持）
2. 创建应用密码（推荐方式）：
   - 登录 WordPress 后台
   - 进入 `用户 > 个人资料`
   - 滚动到"应用密码"部分
   - 输入应用名称（如"AI 文章生成器"）
   - 点击"添加新应用密码"
   - 复制生成的密码

### 3. 配置项目

1. 复制环境变量模板：

```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入你的配置：

```env
# AI模型配置（豆包Doubao）
AI_API_KEY=3add6956-51e3-434e-88da-0d814f2c66ac
AI_API_URL=https://ark.cn-beijing.volces.com/api/v3/chat/completions
AI_MODEL=doubao-seed-1.6-250615

# WordPress配置
WORDPRESS_SITE_URL=https://your-wordpress-site.com
WORDPRESS_USERNAME=your_username
WORDPRESS_APP_PASSWORD=your_app_password_here

# 可选配置
PORT=3000
ARTICLE_LANGUAGE=zh-CN
```

### 4. 安装依赖并启动

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 或启动生产服务器
npm start
```

## 使用方式

### Web 界面使用

1. 打开浏览器访问 `http://localhost:3000`
2. 在左侧表单中输入关键词
3. 选择文章选项（语言、风格、长度等）
4. 点击"生成文章"或"生成并发布"
5. 在右侧预览生成的文章
6. 如需要，可以单独发布文章

### CLI 命令行使用

```bash
# 启动CLI工具
npm run cli

# 或直接运行
node cli.js
```

CLI 提供以下功能：

1. 生成文章
2. 一键生成并发布
3. 检查 WordPress 连接
4. 退出

### API 接口使用

#### 生成文章

```bash
curl -X POST http://localhost:3000/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "keyword": "人工智能",
    "options": {
      "language": "zh-CN",
      "tone": "专业",
      "minLength": 800,
      "maxLength": 1500
    }
  }'
```

#### 发布文章

```bash
curl -X POST http://localhost:3000/api/publish \
  -H "Content-Type: application/json" \
  -d '{
    "title": "文章标题",
    "content": "文章内容",
    "excerpt": "文章摘要",
    "categories": ["技术"],
    "tags": ["AI", "技术"],
    "status": "draft"
  }'
```

#### 一键生成并发布

```bash
curl -X POST http://localhost:3000/api/generate-and-publish \
  -H "Content-Type: application/json" \
  -d '{
    "keyword": "人工智能",
    "options": {
      "language": "zh-CN",
      "tone": "专业"
    },
    "publishOptions": {
      "status": "draft"
    }
  }'
```

## 配置选项详解

### 文章生成选项

| 选项              | 类型    | 默认值 | 说明             |
| ----------------- | ------- | ------ | ---------------- |
| `language`        | string  | zh-CN  | 文章语言         |
| `tone`            | string  | 专业   | 文章风格         |
| `minLength`       | number  | 800    | 最小字数         |
| `maxLength`       | number  | 1500   | 最大字数         |
| `includeHeadings` | boolean | true   | 是否包含标题结构 |

### WordPress 发布选项

| 选项         | 类型   | 默认值 | 说明     |
| ------------ | ------ | ------ | -------- |
| `status`     | string | draft  | 发布状态 |
| `categories` | array  | []     | 文章分类 |
| `tags`       | array  | []     | 文章标签 |

### 发布状态说明

- `draft`: 草稿（不公开显示）
- `publish`: 立即发布（公开显示）
- `pending`: 待审核（需要管理员审核）
- `private`: 私有（仅登录用户可见）

## 最佳实践

### 1. 关键词选择

- 使用具体、明确的关键词
- 避免过于宽泛的词汇
- 可以使用短语或多个相关词汇

### 2. 文章质量优化

- 选择合适的文章长度
- 根据目标受众选择合适的语调
- 启用标题结构以提高可读性

### 3. WordPress 发布策略

- 首次使用建议先生成草稿
- 检查生成的分类和标签是否合适
- 定期检查 WordPress 连接状态

### 4. 安全建议

- 使用应用密码而非账户密码
- 定期更换 API 密钥
- 不要在公共代码库中暴露配置信息

## 故障排除

### 常见错误及解决方案

#### 1. 豆包 API 调用失败

**错误信息**: "文章生成失败"
**可能原因**:

- API 密钥无效或过期
- 网络连接问题
- API 服务暂时不可用

**解决方案**:

- 检查网络连接
- 确认 API 地址是否正确
- 稍后重试

#### 2. WordPress 连接失败

**错误信息**: "WordPress 连接失败"
**可能原因**:

- 站点 URL 错误
- 用户名或密码错误
- WordPress REST API 被禁用

**解决方案**:

- 确认站点 URL 格式正确（包含 https://）
- 检查用户名和应用密码
- 确认 WordPress 版本支持 REST API

#### 3. 文章发布失败

**错误信息**: "文章发布失败"
**可能原因**:

- 用户权限不足
- 分类或标签名称无效
- 文章内容格式问题

**解决方案**:

- 确认用户有发布文章权限
- 检查分类和标签名称
- 验证文章内容格式

### 日志查看

查看详细错误信息：

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 查看HTTP请求日志
tail -f logs/access.log
```

## 高级用法

### 自定义豆包模型参数

在 `.env` 文件中设置：

```env
AI_MODEL=doubao-seed-1.6-250615
AI_MAX_TOKENS=3000
AI_TEMPERATURE=0.8
```

### 批量处理

可以编写脚本调用 API 接口实现批量文章生成：

```javascript
const keywords = ['AI', '机器学习', '深度学习'];
for (const keyword of keywords) {
  // 调用API生成文章
}
```

### 集成到其他系统

项目提供完整的 REST API，可以轻松集成到其他系统中。
