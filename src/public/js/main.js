document.addEventListener('DOMContentLoaded', function () {
  // DOM元素
  const keywordInput = document.getElementById('keyword');
  const languageSelect = document.getElementById('language');
  const toneSelect = document.getElementById('tone');
  const lengthSelect = document.getElementById('length');
  const includeHeadingsCheckbox = document.getElementById('includeHeadings');
  const postStatusSelect = document.getElementById('postStatus');

  const generateBtn = document.getElementById('generateBtn');
  const generateAndPublishBtn = document.getElementById(
    'generateAndPublishBtn'
  );
  const publishBtn = document.getElementById('publishBtn');
  const copyBtn = document.getElementById('copyBtn');
  const checkConnectionBtn = document.getElementById('checkConnectionBtn');

  const loadingIndicator = document.getElementById('loadingIndicator');
  const loadingText = document.getElementById('loadingText');
  const articlePreview = document.getElementById('articlePreview');
  const emptyState = document.getElementById('emptyState');
  const errorState = document.getElementById('errorState');

  const articleTitle = document.getElementById('articleTitle');
  const articleMeta = document.getElementById('articleMeta');
  const articleContent = document.getElementById('articleContent');

  const statusBadge = document.getElementById('statusBadge');

  const siteInfoContainer = document.getElementById('siteInfoContainer');
  const siteInfo = document.getElementById('siteInfo');

  // 当前文章数据
  let currentArticle = null;

  // API基础URL
  const apiBaseUrl = '/api';

  // 显示加载状态
  function showLoading(message = '正在生成文章，请稍候...') {
    loadingIndicator.classList.remove('d-none');
    loadingText.textContent = message;
    articlePreview.classList.add('d-none');
    emptyState.classList.add('d-none');
    errorState.classList.add('d-none');
  }

  // 显示错误
  function showError(message) {
    loadingIndicator.classList.add('d-none');
    articlePreview.classList.add('d-none');
    emptyState.classList.add('d-none');
    errorState.classList.remove('d-none');
    errorState.textContent = message;
  }

  // 显示文章预览
  function showArticlePreview(article) {
    loadingIndicator.classList.add('d-none');
    articlePreview.classList.remove('d-none');
    emptyState.classList.add('d-none');
    errorState.classList.add('d-none');

    // 保存当前文章
    currentArticle = article;

    // 设置标题
    articleTitle.textContent = article.title;

    // 设置元数据
    let metaHtml = '';
    if (article.categories && article.categories.length > 0) {
      metaHtml += `<span class="me-3"><i class="bi bi-folder me-1"></i>${article.categories.join(
        ', '
      )}</span>`;
    }
    if (article.tags && article.tags.length > 0) {
      metaHtml += `<span><i class="bi bi-tags me-1"></i>${article.tags.join(
        ', '
      )}</span>`;
    }
    articleMeta.innerHTML = metaHtml;

    // 设置内容（直接使用HTML内容）
    articleContent.innerHTML = article.content;

    // 显示操作按钮
    publishBtn.classList.remove('d-none');
    copyBtn.classList.remove('d-none');
    statusBadge.classList.remove('d-none');
    statusBadge.textContent = '未发布';
    statusBadge.className = 'badge bg-secondary status-badge';
  }

  // 更新发布状态
  function updatePublishStatus(status) {
    statusBadge.classList.remove('d-none');

    switch (status) {
      case 'publish':
        statusBadge.textContent = '已发布';
        statusBadge.className = 'badge bg-success status-badge';
        break;
      case 'draft':
        statusBadge.textContent = '草稿';
        statusBadge.className = 'badge bg-secondary status-badge';
        break;
      case 'pending':
        statusBadge.textContent = '待审核';
        statusBadge.className = 'badge bg-warning status-badge';
        break;
      default:
        statusBadge.textContent = status;
        statusBadge.className = 'badge bg-info status-badge';
    }
  }

  // 获取文章生成选项
  function getArticleOptions() {
    const aiServiceSelect = document.getElementById('aiService');

    // 根据长度选择确定字数范围
    let minLength, maxLength;
    switch (lengthSelect.value) {
      case 'short':
        minLength = 500;
        maxLength = 800;
        break;
      case 'long':
        minLength = 1500;
        maxLength = 2500;
        break;
      case 'medium':
      default:
        minLength = 800;
        maxLength = 1500;
    }

    // 获取图片选项
    const imageOptions = window.getImageOptions
      ? window.getImageOptions()
      : { enabled: false };

    return {
      aiService: aiServiceSelect.value, // 新增：AI服务选择
      // language: languageSelect.value,
      tone: toneSelect.value,
      minLength,
      maxLength,
      includeHeadings: includeHeadingsCheckbox.checked,
      imageOptions: imageOptions,
    };
  }

  // 生成文章
  async function generateArticle() {
    const keyword = keywordInput.value.trim();
    if (!keyword) {
      alert('请输入关键词');
      keywordInput.focus();
      return;
    }

    showLoading();

    try {
      const response = await fetch(`${apiBaseUrl}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          keyword,
          options: getArticleOptions(),
        }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || '生成文章失败');
      }

      showArticlePreview(result.data);
    } catch (error) {
      showError(`生成文章失败: ${error.message}`);
    }
  }

  // 发布文章
  async function publishArticle(article) {
    if (!article) {
      alert('没有可发布的文章');
      return;
    }

    showLoading('正在发布文章到WordPress...');

    try {
      const publishData = {
        title: article.title,
        content: article.content,
        excerpt: article.excerpt,
        categories: article.categories,
        tags: article.tags,
        status: postStatusSelect.value,
      };

      const response = await fetch(`${apiBaseUrl}/publish`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(publishData),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || '发布文章失败');
      }

      // 更新UI状态
      updatePublishStatus(result.data.status);

      // 如果有文章链接，添加到元数据
      if (result.data.postUrl) {
        const linkElement = document.createElement('div');
        linkElement.innerHTML = `<a href="${result.data.postUrl}" target="_blank" class="mt-2 d-inline-block">
          <i class="bi bi-link-45deg"></i> 查看文章
        </a>`;
        articleMeta.appendChild(linkElement);
      }

      // 显示文章预览
      articlePreview.classList.remove('d-none');
      loadingIndicator.classList.add('d-none');

      return result.data;
    } catch (error) {
      showError(`发布文章失败: ${error.message}`);
      throw error;
    }
  }

  // 一键生成并发布
  async function generateAndPublish() {
    const keyword = keywordInput.value.trim();
    if (!keyword) {
      alert('请输入关键词');
      keywordInput.focus();
      return;
    }

    showLoading('正在生成并发布文章...');

    try {
      const response = await fetch(`${apiBaseUrl}/generate-and-publish`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          keyword,
          options: getArticleOptions(),
          publishOptions: {
            status: postStatusSelect.value,
          },
        }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || '生成并发布文章失败');
      }

      // 显示文章预览
      showArticlePreview(result.data.article);

      // 更新发布状态
      updatePublishStatus(result.data.publishResult.status);

      // 如果有文章链接，添加到元数据
      if (result.data.publishResult.postUrl) {
        const linkElement = document.createElement('div');
        linkElement.innerHTML = `<a href="${result.data.publishResult.postUrl}" target="_blank" class="mt-2 d-inline-block">
          <i class="bi bi-link-45deg"></i> 查看文章
        </a>`;
        articleMeta.appendChild(linkElement);
      }
    } catch (error) {
      showError(`生成并发布文章失败: ${error.message}`);
    }
  }

  // 检查WordPress连接
  async function checkWordPressConnection() {
    try {
      siteInfoContainer.classList.add('d-none');

      const response = await fetch(`${apiBaseUrl}/site-info`);
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'WordPress连接失败');
      }

      // 显示站点信息
      siteInfoContainer.classList.remove('d-none');
      siteInfo.innerHTML = `
        <p class="mb-1"><strong>站点名称:</strong> ${result.data.name}</p>
        <p class="mb-1"><strong>站点描述:</strong> ${
          result.data.description || '无'
        }</p>
        <p class="mb-0"><strong>站点URL:</strong> <a href="${
          result.data.url
        }" target="_blank">${result.data.url}</a></p>
      `;
    } catch (error) {
      alert(`WordPress连接失败: ${error.message}`);
    }
  }

  // 复制文章内容
  function copyArticleContent() {
    if (!currentArticle) return;

    const content = `# ${currentArticle.title}\n\n${currentArticle.content}`;
    navigator.clipboard
      .writeText(content)
      .then(() => alert('文章内容已复制到剪贴板'))
      .catch((err) => alert('复制失败: ' + err.message));
  }

  // 事件监听
  generateBtn.addEventListener('click', generateArticle);
  generateAndPublishBtn.addEventListener('click', generateAndPublish);
  publishBtn.addEventListener('click', () => publishArticle(currentArticle));
  copyBtn.addEventListener('click', copyArticleContent);
  checkConnectionBtn.addEventListener('click', checkWordPressConnection);
});

// 图片方式切换控制
document.addEventListener('DOMContentLoaded', function () {
  const methodRadios = document.querySelectorAll('input[name="imageMethod"]');
  const aiOptions = document.getElementById('aiServiceOptions');
  const searchOptions = document.getElementById('searchServiceOptions');
  const includeImagesCheckbox = document.getElementById('includeImages');
  const imageOptionsContainer = document.getElementById(
    'imageOptionsContainer'
  );

  // 图片包含选项控制
  includeImagesCheckbox.addEventListener('change', function () {
    imageOptionsContainer.style.display = this.checked ? 'block' : 'none';
  });

  // 图片生成方式切换
  methodRadios.forEach((radio) => {
    radio.addEventListener('change', function () {
      if (this.value === 'ai') {
        aiOptions.style.display = 'block';
        searchOptions.style.display = 'none';
      } else {
        aiOptions.style.display = 'none';
        searchOptions.style.display = 'block';
      }
    });
  });

  // 获取图片选项
  function getImageOptions() {
    const includeImages = includeImagesCheckbox.checked;

    if (!includeImages) {
      return { enabled: false };
    }

    const method = document.querySelector(
      'input[name="imageMethod"]:checked'
    ).value;

    // 解析尺寸字符串 (例如: "1024x1024")
    const sizeValue = document.getElementById('imageSize').value;
    const [width, height] = sizeValue.split('x').map((num) => parseInt(num));

    const baseOptions = {
      enabled: true,
      width: width,
      height: height,
      maxRetries: 2,
    };

    if (method === 'ai') {
      const aiService = document.getElementById('aiImageService').value;
      return {
        ...baseOptions,
        method: 'ai',
        aiService: aiService,
      };
    } else {
      const searchService = document.getElementById('searchImageService').value;
      return {
        ...baseOptions,
        method: 'search',
        searchService: searchService,
      };
    }
  }

  // 更新全局函数
  window.getImageOptions = getImageOptions;

  // 初始化推荐关键词点击事件
  initKeywordTags();
});

// 初始化关键词标签点击功能
function initKeywordTags() {
  const keywordTags = document.querySelectorAll('.keyword-tag');
  const keywordInput = document.getElementById('keyword');

  keywordTags.forEach((tag) => {
    tag.addEventListener('click', function () {
      const keyword = this.getAttribute('data-keyword');
      keywordInput.value = keyword;

      // 添加视觉反馈
      this.style.transform = 'scale(0.95)';
      setTimeout(() => {
        this.style.transform = 'scale(1)';
      }, 150);

      // 可选：自动聚焦到输入框
      keywordInput.focus();
    });

    // 添加悬停效果
    tag.style.cursor = 'pointer';
    tag.addEventListener('mouseenter', function () {
      this.style.transform = 'scale(1.05)';
    });

    tag.addEventListener('mouseleave', function () {
      this.style.transform = 'scale(1)';
    });
  });
}
