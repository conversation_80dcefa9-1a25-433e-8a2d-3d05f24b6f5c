<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI文章生成器 - WordPress自动发布</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css"
    />
    <style>
      body {
        background-color: #f8f9fa;
      }
      .container {
        max-width: 1200px;
      }
      .card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      .card-header {
        background-color: #f1f8ff;
        border-bottom: 1px solid #e3f2fd;
      }
      .btn-primary {
        background-color: #0d6efd;
        border-color: #0d6efd;
      }
      .btn-outline-primary {
        color: #0d6efd;
        border-color: #0d6efd;
      }
      .form-control:focus {
        border-color: #86b7fe;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
      }
      #articlePreview {
        max-height: 500px;
        overflow-y: auto;
      }
      .loading {
        display: inline-block;
        width: 1.5rem;
        height: 1.5rem;
        border: 3px solid rgba(0, 0, 0, 0.2);
        border-radius: 50%;
        border-top-color: #0d6efd;
        animation: spin 1s ease-in-out infinite;
      }
      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }

      /* 关键词标签样式 */
      .keyword-tag {
        transition: all 0.2s ease;
        cursor: pointer !important;
        user-select: none;
      }
      .keyword-tag:hover {
        transform: scale(1.05) !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
      .keyword-tag:active {
        transform: scale(0.95) !important;
      }
      .status-badge {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
      }
    </style>
  </head>
  <body>
    <div class="container py-4">
      <header class="pb-3 mb-4 border-bottom">
        <div class="d-flex align-items-center text-dark text-decoration-none">
          <i class="bi bi-robot fs-4 me-2"></i>
          <span class="fs-4">AI文章生成器 - WordPress自动发布</span>
        </div>
      </header>

      <div class="row">
        <!-- 左侧表单 -->
        <div class="col-md-5">
          <div class="card mb-4">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-pencil-square me-2"></i>文章生成
              </h5>
            </div>
            <div class="card-body">
              <form id="articleForm">
                <div class="mb-3">
                  <label for="aiService" class="form-label">AI Service</label>
                  <select class="form-select" id="aiService">
                    <option value="gpt4o" selected>GPT-4o (默认推荐)</option>
                    <option value="deepseek">DeepSeek (智能对话)</option>
                    <option value="doubao">豆包 (Doubao)</option>
                  </select>
                  <small class="text-muted"
                    >Choose the AI service for article generation</small
                  >
                </div>

                <div class="mb-3">
                  <label for="keyword" class="form-label">Keyword</label>
                  <input
                    type="text"
                    class="form-control"
                    id="keyword"
                    placeholder="Enter article keyword"
                    required
                  />

                  <!-- 推荐关键词 -->
                  <div class="mt-2">
                    <small class="text-muted">💡 Recommended Keywords:</small>
                    <div class="mt-1" id="recommendedKeywords">
                      <!-- Education Related -->
                      <span
                        class="badge bg-primary me-1 mb-1 keyword-tag"
                        data-keyword="online math education"
                        >online math education</span
                      >
                      <span
                        class="badge bg-primary me-1 mb-1 keyword-tag"
                        data-keyword="STEM education programs"
                        >STEM education programs</span
                      >
                      <span
                        class="badge bg-primary me-1 mb-1 keyword-tag"
                        data-keyword="math learning strategies"
                        >math learning strategies</span
                      >
                      <span
                        class="badge bg-primary me-1 mb-1 keyword-tag"
                        data-keyword="elementary math tutoring"
                        >elementary math tutoring</span
                      >
                      <span
                        class="badge bg-primary me-1 mb-1 keyword-tag"
                        data-keyword="digital learning platforms"
                        >digital learning platforms</span
                      >

                      <!-- Regional Education -->
                      <span
                        class="badge bg-success me-1 mb-1 keyword-tag"
                        data-keyword="Canadian education system"
                        >Canadian education system</span
                      >
                      <span
                        class="badge bg-success me-1 mb-1 keyword-tag"
                        data-keyword="international student programs"
                        >international student programs</span
                      >
                      <span
                        class="badge bg-success me-1 mb-1 keyword-tag"
                        data-keyword="North American curriculum"
                        >North American curriculum</span
                      >
                      <span
                        class="badge bg-success me-1 mb-1 keyword-tag"
                        data-keyword="multicultural education"
                        >multicultural education</span
                      >

                      <!-- Learning Methods -->
                      <span
                        class="badge bg-info me-1 mb-1 keyword-tag"
                        data-keyword="effective study techniques"
                        >effective study techniques</span
                      >
                      <span
                        class="badge bg-info me-1 mb-1 keyword-tag"
                        data-keyword="online learning platforms"
                        >online learning platforms</span
                      >
                      <span
                        class="badge bg-info me-1 mb-1 keyword-tag"
                        data-keyword="math competition training"
                        >math competition training</span
                      >
                      <span
                        class="badge bg-info me-1 mb-1 keyword-tag"
                        data-keyword="educational technology"
                        >educational technology</span
                      >

                      <!-- Age-Specific Learning -->
                      <span
                        class="badge bg-warning me-1 mb-1 keyword-tag"
                        data-keyword="early childhood education"
                        >early childhood education</span
                      >
                      <span
                        class="badge bg-warning me-1 mb-1 keyword-tag"
                        data-keyword="middle school math skills"
                        >middle school math skills</span
                      >
                      <span
                        class="badge bg-warning me-1 mb-1 keyword-tag"
                        data-keyword="test preparation strategies"
                        >test preparation strategies</span
                      >

                      <!-- Trending Topics -->
                      <span
                        class="badge bg-secondary me-1 mb-1 keyword-tag"
                        data-keyword="AI in education"
                        >AI in education</span
                      >
                      <span
                        class="badge bg-secondary me-1 mb-1 keyword-tag"
                        data-keyword="personalized learning"
                        >personalized learning</span
                      >
                      <span
                        class="badge bg-secondary me-1 mb-1 keyword-tag"
                        data-keyword="homeschooling methods"
                        >homeschooling methods</span
                      >
                      <span
                        class="badge bg-secondary me-1 mb-1 keyword-tag"
                        data-keyword="study habits development"
                        >study habits development</span
                      >
                    </div>
                    <small class="text-muted"
                      >Click tags to quickly select keywords</small
                    >
                  </div>
                </div>

                <!-- <div class="mb-3">
                  <label for="language" class="form-label">语言</label>
                  <select class="form-select" id="language">
                    <option value="zh-CN" selected>中文</option>
                    <option value="en-US">英文</option>
                  </select>
                </div> -->

                <div class="mb-3">
                  <label for="tone" class="form-label">文章风格</label>
                  <select class="form-select" id="tone">
                    <option value="专业" selected>专业</option>
                    <option value="轻松">轻松</option>
                    <option value="幽默">幽默</option>
                    <option value="严肃">严肃</option>
                  </select>
                </div>

                <div class="mb-3">
                  <label for="length" class="form-label">文章长度</label>
                  <select class="form-select" id="length">
                    <option value="short">短文 (500-800字)</option>
                    <option value="medium" selected>中等 (800-1500字)</option>
                    <option value="long">长文 (1500-2500字)</option>
                  </select>
                </div>

                <div class="mb-3 form-check">
                  <input
                    type="checkbox"
                    class="form-check-input"
                    id="includeHeadings"
                    checked
                  />
                  <label class="form-check-label" for="includeHeadings"
                    >包含标题结构</label
                  >
                </div>

                <div class="d-grid gap-2">
                  <button
                    type="button"
                    id="generateBtn"
                    class="btn btn-primary"
                  >
                    <i class="bi bi-magic me-2"></i>生成文章
                  </button>
                  <button
                    type="button"
                    id="generateAndPublishBtn"
                    class="btn btn-outline-primary"
                  >
                    <i class="bi bi-cloud-upload me-2"></i>生成并发布
                  </button>
                </div>
              </form>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-wordpress me-2"></i>WordPress设置
              </h5>
            </div>
            <div class="card-body">
              <div class="mb-3">
                <label for="postStatus" class="form-label">发布状态</label>
                <select class="form-select" id="postStatus">
                  <option value="draft" selected>草稿</option>
                  <option value="publish">立即发布</option>
                  <option value="pending">待审核</option>
                </select>
              </div>

              <div id="siteInfoContainer" class="alert alert-info d-none">
                <div id="siteInfo"></div>
              </div>

              <div class="d-grid">
                <button
                  type="button"
                  id="checkConnectionBtn"
                  class="btn btn-outline-secondary"
                >
                  <i class="bi bi-check-circle me-2"></i>检查WordPress连接
                </button>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h5 class="mb-0"><i class="bi bi-image me-2"></i>图片设置</h5>
            </div>
            <div class="card-body">
              <div class="mb-3 form-check">
                <input
                  type="checkbox"
                  class="form-check-input"
                  id="includeImages"
                  checked
                />
                <label class="form-check-label" for="includeImages"
                  >包含图片</label
                >
              </div>

              <div id="imageOptionsContainer">
                <!-- 第一层：选择生成方式 -->
                <div class="mb-3">
                  <label class="form-label">图片生成方式</label>
                  <div class="btn-group w-100" role="group">
                    <input
                      type="radio"
                      class="btn-check"
                      name="imageMethod"
                      id="methodAI"
                      value="ai"
                      checked
                    />
                    <label class="btn btn-outline-primary" for="methodAI">
                      <i class="bi bi-magic me-1"></i>AI生成
                    </label>

                    <input
                      type="radio"
                      class="btn-check"
                      name="imageMethod"
                      id="methodSearch"
                      value="search"
                    />
                    <label class="btn btn-outline-primary" for="methodSearch">
                      <i class="bi bi-search me-1"></i>搜索图片
                    </label>
                  </div>
                  <div class="form-text">
                    AI生成相关度最高但有成本，搜索图片免费但相关度较低
                  </div>
                </div>

                <!-- AI服务选择 -->
                <div id="aiServiceOptions" class="mb-3">
                  <label for="aiImageService" class="form-label"
                    >AI图片服务</label
                  >
                  <select class="form-select" id="aiImageService">
                    <option value="tal" selected>
                      TAL AI（好未来豆包，推荐）
                    </option>
                    <option value="stability">Stability AI（经典选择）</option>
                    <option value="openai">OpenAI DALL-E 3（质量最好）</option>
                    <option value="midjourney">Midjourney（艺术风格）</option>
                  </select>
                  <div class="form-text">
                    <small>
                      • TAL AI: 好未来豆包模型，中文理解好，速度快<br />
                      • Stability AI: 速度快，性价比高<br />
                      • DALL-E 3: 质量最高<br />
                      • Midjourney: 艺术效果好
                    </small>
                  </div>
                </div>

                <!-- 搜索服务选择 -->
                <div
                  id="searchServiceOptions"
                  class="mb-3"
                  style="display: none"
                >
                  <label for="searchImageService" class="form-label"
                    >搜索图片服务</label
                  >
                  <select class="form-select" id="searchImageService">
                    <option value="pexels" selected>
                      Pexels（专业摄影，免费）✅
                    </option>
                    <option value="pixabay">Pixabay（基础免费）</option>
                    <option value="unsplash">
                      Unsplash（高质量免费）⚠️需要API密钥
                    </option>
                    <option value="shutterstock">
                      Shutterstock（最高质量，付费）⚠️网络问题
                    </option>
                    <option value="getty">
                      Getty Images（顶级专业，付费）
                    </option>
                  </select>
                  <div class="form-text">
                    <small>
                      • Shutterstock: 最高质量，4亿+专业图片<br />
                      • Getty Images: 顶级新闻和专业图片<br />
                      • Unsplash: 高质量免费图片<br />
                      • Pexels: 专业摄影作品，免费<br />
                      • Pixabay: 基础免费图片
                    </small>
                  </div>
                </div>

                <!-- 图片尺寸 -->
                <div class="row">
                  <div class="col-12">
                    <label for="imageSize" class="form-label">图片尺寸</label>
                    <select class="form-select" id="imageSize">
                      <option value="1024x1024" selected>
                        1024x1024 (正方形)
                      </option>
                      <option value="1152x896">1152x896 (横向)</option>
                      <option value="1216x832">1216x832 (横向)</option>
                      <option value="1344x768">1344x768 (宽屏)</option>
                      <option value="1536x640">1536x640 (超宽)</option>
                      <option value="896x1152">896x1152 (竖向)</option>
                      <option value="832x1216">832x1216 (竖向)</option>
                      <option value="768x1344">768x1344 (高屏)</option>
                      <option value="640x1536">640x1536 (超高)</option>
                    </select>
                    <div class="form-text">
                      <small>选择Stability AI支持的标准尺寸</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧预览 -->
        <div class="col-md-7">
          <div class="card">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h5 class="mb-0"><i class="bi bi-eye me-2"></i>文章预览</h5>
              <div>
                <span
                  id="statusBadge"
                  class="badge bg-secondary status-badge d-none"
                  >草稿</span
                >
                <button
                  id="publishBtn"
                  class="btn btn-sm btn-success ms-2 d-none"
                >
                  <i class="bi bi-cloud-upload"></i> 发布
                </button>
                <button
                  id="copyBtn"
                  class="btn btn-sm btn-outline-secondary ms-2 d-none"
                >
                  <i class="bi bi-clipboard"></i> 复制
                </button>
              </div>
            </div>
            <div class="card-body">
              <div id="loadingIndicator" class="text-center py-5 d-none">
                <div class="loading mb-3"></div>
                <p class="text-muted" id="loadingText">
                  正在生成文章，请稍候...
                </p>
              </div>

              <div id="articlePreview" class="d-none">
                <h3 id="articleTitle" class="mb-3"></h3>
                <div id="articleMeta" class="mb-3 small text-muted"></div>
                <div id="articleContent"></div>
              </div>

              <div id="emptyState" class="text-center py-5">
                <i class="bi bi-file-earmark-text fs-1 text-muted"></i>
                <p class="mt-3 text-muted">
                  Enter a keyword and click "Generate Article" to start creating
                </p>
              </div>

              <div id="errorState" class="alert alert-danger d-none"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="js/main.js"></script>
  </body>
</html>
