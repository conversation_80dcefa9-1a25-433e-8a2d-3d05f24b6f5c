const logger = require('../config/logger');
const { generateArticle } = require('../services/aiServiceSegmented');
const { publishArticle, getSiteInfo } = require('../services/wordpressService');

/**
 * 生成文章
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
async function generateArticleHandler(req, res) {
  try {
    const { keyword, options } = req.body;

    if (!keyword) {
      return res.status(400).json({
        success: false,
        message: '关键词不能为空',
      });
    }

    logger.info(`收到文章生成请求，关键词: ${keyword}`);

    const article = await generateArticle(keyword, options);

    return res.status(200).json({
      success: true,
      message: '文章生成成功',
      data: article,
    });
  } catch (error) {
    logger.error(`文章生成处理失败: ${error.message}`, { error });
    return res.status(500).json({
      success: false,
      message: `文章生成失败: ${error.message}`,
    });
  }
}

/**
 * 发布文章到WordPress
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
async function publishArticleHandler(req, res) {
  try {
    const article = req.body;

    if (!article || !article.title || !article.content) {
      return res.status(400).json({
        success: false,
        message: '文章标题和内容不能为空',
      });
    }

    logger.info(`收到文章发布请求，标题: ${article.title}`);

    const result = await publishArticle(article);

    return res.status(200).json({
      success: true,
      message: '文章发布成功',
      data: result,
    });
  } catch (error) {
    logger.error(`文章发布处理失败: ${error.message}`, { error });
    return res.status(500).json({
      success: false,
      message: `文章发布失败: ${error.message}`,
    });
  }
}

/**
 * 一键生成并发布文章
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
async function generateAndPublishHandler(req, res) {
  try {
    const { keyword, options, publishOptions } = req.body;

    if (!keyword) {
      return res.status(400).json({
        success: false,
        message: '关键词不能为空',
      });
    }

    logger.info(`收到一键生成并发布请求，关键词: ${keyword}`);

    // 1. 生成文章
    const article = await generateArticle(keyword, options);

    // 调试：打印豆包生成的原始结果
    console.log('=== 豆包生成的原始结果 ===');
    console.log('标题:', article.title);
    console.log('摘要:', article.excerpt);
    console.log('分类:', article.categories);
    console.log('标签:', article.tags);
    console.log('内容长度:', article.content.length);
    console.log('内容前500字符:');
    console.log(article.content.substring(0, 500));
    console.log('=== 原始结果结束 ===');

    // 2. 准备发布数据
    // 从内容中提取真正的标题、摘要、分类和标签
    let realTitle = article.title;
    let realExcerpt = article.excerpt;
    let realCategories = article.categories;
    let realTags = article.tags;

    if (realTitle === '元数据') {
      // 从内容中提取元数据
      const titleMatch = article.content.match(/文章标题[：:]\s*(.+?)[\n\r]/);
      if (titleMatch) {
        realTitle = titleMatch[1].trim();
      }

      const excerptMatch = article.content.match(/文章摘要[：:]\s*(.+?)[\n\r]/);
      if (excerptMatch) {
        realExcerpt = excerptMatch[1].trim();
      }

      const categoriesMatch = article.content.match(
        /WordPress分类[：:]\s*(.+?)[\n\r]/
      );
      if (categoriesMatch) {
        realCategories = categoriesMatch[1]
          .split(/[,，、]/)
          .map((cat) => cat.trim());
      }

      const tagsMatch = article.content.match(
        /WordPress标签[：:]\s*(.+?)[\n\r]/
      );
      if (tagsMatch) {
        realTags = tagsMatch[1].split(/[,，、]/).map((tag) => tag.trim());
      }
    }

    // 清理内容：移除元数据部分，只保留正文
    let htmlContent = article.content;

    // 移除元数据部分（从开头到第一个真正的标题）
    htmlContent = htmlContent.replace(/^[\s\S]*?(?=#+\s+[^元数据])/m, '');

    // 如果没有找到标题，尝试移除元数据列表
    if (htmlContent === article.content) {
      htmlContent = htmlContent.replace(/^### 元数据[\s\S]*?(?=#+\s+)/m, '');
    }

    // 如果还是没有清理干净，手动移除元数据部分
    if (htmlContent.includes('### 元数据')) {
      const lines = htmlContent.split('\n');
      let startIndex = -1;
      let endIndex = -1;

      for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes('### 元数据')) {
          startIndex = i;
        }
        if (startIndex !== -1 && lines[i].match(/^#+\s+[^元数据]/)) {
          endIndex = i;
          break;
        }
      }

      if (startIndex !== -1) {
        if (endIndex !== -1) {
          lines.splice(startIndex, endIndex - startIndex);
        } else {
          // 如果没有找到结束位置，移除前8行（通常元数据部分）
          lines.splice(startIndex, 8);
        }
        htmlContent = lines.join('\n');
      }
    }

    // 将Markdown转换为HTML
    // 替换标题
    htmlContent = htmlContent.replace(/^###\s+(.+?)$/gm, '<h3>$1</h3>');
    htmlContent = htmlContent.replace(/^##\s+(.+?)$/gm, '<h2>$1</h2>');
    htmlContent = htmlContent.replace(/^#\s+(.+?)$/gm, '<h1>$1</h1>');

    // 替换粗体
    htmlContent = htmlContent.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');

    // 替换列表
    htmlContent = htmlContent.replace(/^\s*-\s+(.+?)$/gm, '<li>$1</li>');
    htmlContent = htmlContent.replace(/(<li>.*?<\/li>\s*)+/gs, '<ul>$&</ul>');

    // 清理空行并替换段落
    htmlContent = htmlContent.replace(/^\s*$/gm, ''); // 移除空行
    htmlContent = htmlContent.replace(/^(?!<[hul]|<li)(.+?)$/gm, '<p>$1</p>'); // 包装段落

    // 清理多余的空白
    htmlContent = htmlContent.replace(/\n\s*\n/g, '\n').trim();

    // 调试：打印处理后的结果
    console.log('=== 处理后的结果 ===');
    console.log('处理后标题:', realTitle);
    console.log('处理后摘要:', realExcerpt);
    console.log('处理后分类:', realCategories);
    console.log('处理后标签:', realTags);
    console.log('处理后内容长度:', htmlContent.length);
    console.log('处理后内容前500字符:');
    console.log(htmlContent.substring(0, 500));
    console.log('=== 处理后结果结束 ===');

    // 调试：检查AI生成的SEO字段
    console.log('=== AI生成的SEO字段 ===');
    console.log('seoTitle:', article.seoTitle);
    console.log('metaDescription:', article.metaDescription);
    console.log('focusKeyword:', article.focusKeyword);
    console.log('slug:', article.slug);
    console.log('=== SEO字段结束 ===');

    const publishData = {
      title: realTitle,
      content: htmlContent,
      excerpt: realExcerpt,
      categories: realCategories,
      tags: realTags,
      status: publishOptions?.status || 'draft', // 默认为草稿
      slug: article.slug, // 只传递slug，SEO字段单独处理
      // SEO字段通过article对象传递给publishArticle函数
      seoTitle: article.seoTitle,
      metaDescription: article.metaDescription,
      focusKeyword: article.focusKeyword,
    };

    // 3. 发布文章
    const publishResult = await publishArticle(publishData);

    return res.status(200).json({
      success: true,
      message: '文章已成功生成并发布',
      data: {
        article,
        publishResult,
      },
    });
  } catch (error) {
    logger.error(`一键生成并发布处理失败: ${error.message}`, { error });
    return res.status(500).json({
      success: false,
      message: `一键生成并发布失败: ${error.message}`,
    });
  }
}

/**
 * 获取WordPress站点信息
 * @param {Object} _req - Express请求对象（未使用）
 * @param {Object} res - Express响应对象
 */
async function getSiteInfoHandler(_req, res) {
  try {
    const siteInfo = await getSiteInfo();

    return res.status(200).json({
      success: true,
      data: siteInfo,
    });
  } catch (error) {
    logger.error(`获取站点信息处理失败: ${error.message}`, { error });
    return res.status(500).json({
      success: false,
      message: `获取站点信息失败: ${error.message}`,
    });
  }
}

module.exports = {
  generateArticleHandler,
  publishArticleHandler,
  generateAndPublishHandler,
  getSiteInfoHandler,
};
