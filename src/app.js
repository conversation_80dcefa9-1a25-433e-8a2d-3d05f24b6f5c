const express = require('express');
const path = require('path');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const fs = require('fs');
const { config, validateConfig } = require('./config/config');
const logger = require('./config/logger');
const apiRoutes = require('./routes/api');
// 中间件暂时移除

// 创建Express应用
const app = express();

// 安全中间件
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", 'cdn.jsdelivr.net'],
        styleSrc: ["'self'", 'cdn.jsdelivr.net', "'unsafe-inline'"],
        imgSrc: [
          "'self'",
          'data:',
          'download-pa-s3.thethinkacademy.com',
          'pa-s3-prod.s3.us-west-2.amazonaws.com',
        ],
        connectSrc: ["'self'"],
        fontSrc: ["'self'", 'cdn.jsdelivr.net'],
        objectSrc: ["'none'"],
        upgradeInsecureRequests: [],
      },
    },
  })
);

// CORS配置
app.use(cors());

// 请求体解析
app.use(express.json({ limit: '5mb' }));
app.use(express.urlencoded({ extended: true, limit: '5mb' }));

// 日志中间件
// 确保日志目录存在
const logDir = path.dirname(config.logging.file);
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 创建HTTP请求日志流
const accessLogStream = fs.createWriteStream(path.join(logDir, 'access.log'), {
  flags: 'a',
});

// 使用Morgan记录HTTP请求
app.use(morgan('combined', { stream: accessLogStream }));
if (process.env.NODE_ENV !== 'production') {
  app.use(morgan('dev')); // 开发环境下在控制台也显示请求日志
}

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// 速率限制暂时移除

// API路由
app.use('/api', apiRoutes);

// 主页路由
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 错误处理中间件
app.use((err, req, res, next) => {
  logger.error(`应用错误: ${err.message}`, { error: err, path: req.path });

  res.status(err.status || 500).json({
    success: false,
    message:
      process.env.NODE_ENV === 'production' ? '服务器内部错误' : err.message,
  });
});

// 404处理
app.use((req, res) => {
  logger.warn(`404 Not Found: ${req.method} ${req.path}`);

  res.status(404).json({
    success: false,
    message: '请求的资源不存在',
  });
});

// 启动服务器
async function startServer() {
  try {
    // 验证必要的配置
    validateConfig();

    // 启动HTTP服务器
    const PORT = config.server.port;
    const HOST = config.server.host;

    app.listen(PORT, HOST, () => {
      logger.info(`服务器已启动: http://${HOST}:${PORT}`);
      console.log(`服务器已启动: http://${HOST}:${PORT}`);
    });
  } catch (error) {
    logger.error(`服务器启动失败: ${error.message}`, { error });
    console.error(`服务器启动失败: ${error.message}`);
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  logger.error(`未捕获的异常: ${error.message}`, { error });
  console.error(`未捕获的异常: ${error.message}`);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error(`未处理的Promise拒绝: ${reason}`, { reason });
  console.error(`未处理的Promise拒绝: ${reason}`);
});

// 启动服务器
startServer();
