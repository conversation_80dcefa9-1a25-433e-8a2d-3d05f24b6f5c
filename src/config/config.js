require('dotenv').config();

const config = {
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost',
  },

  // AI模型配置（支持多种AI服务）
  ai: {
    defaultService: process.env.DEFAULT_AI_SERVICE || 'gpt4o',
    maxTokens: parseInt(process.env.AI_MAX_TOKENS) || 12000,
    temperature: parseFloat(process.env.AI_TEMPERATURE) || 0.7,

    // 豆包配置
    doubao: {
      apiKey: process.env.DOUBAO_API_KEY,
      apiUrl:
        process.env.DOUBAO_API_URL ||
        'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
      model: process.env.DOUBAO_MODEL || 'doubao-seed-1-6-flash-250715',
    },

    // GPT-4o配置
    gpt4o: {
      apiKey: process.env.GPT4O_API_KEY,
      apiUrl:
        process.env.GPT4O_API_URL ||
        'http://ai-service.tal.com/openai-compatible/v1/chat/completions',
      model: process.env.GPT4O_MODEL || 'gpt-4o',
    },

    // DeepSeek配置
    deepseek: {
      apiKey: process.env.DEEPSEEK_API_KEY,
      apiUrl:
        process.env.DEEPSEEK_API_URL ||
        'http://ai-service.tal.com/openai-compatible/v1/chat/completions',
      model: process.env.DEEPSEEK_MODEL || 'deepseek-chat',
    },
  },

  // WordPress配置
  wordpress: {
    siteUrl: process.env.WORDPRESS_SITE_URL,
    username: process.env.WORDPRESS_USERNAME,
    password: process.env.WORDPRESS_PASSWORD,
    applicationPassword: process.env.WORDPRESS_APP_PASSWORD,
  },

  // 图片服务配置
  image: {
    apiKeys: {
      openai: process.env.OPENAI_API_KEY,
      stability: process.env.STABILITY_API_KEY,
      pixabay: process.env.PIXABAY_API_KEY,
      pexels: process.env.PEXELS_API_KEY,
      unsplash: process.env.UNSPLASH_ACCESS_KEY,
      shutterstock: process.env.SHUTTERSTOCK_API_KEY,
      shutterstockSecret: process.env.SHUTTERSTOCK_SECRET,
      getty: process.env.GETTY_API_KEY,
      tal:
        process.env.TAL_API_KEY || '300000267:af195c7bdc5939920d50d4088cde3114',
    },
    tal: {
      apiUrl:
        process.env.TAL_IMAGE_API_URL ||
        'https://ai-service.tal.com/openai-compatible/v1/images/generations',
      model: process.env.TAL_IMAGE_MODEL || 'doubao-seedream-3-0-t2i',
      defaultSize: '1024x1024',
      guidanceScale: 7.5,
      watermark: false,
    },
    aws: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION || 'us-east-1',
      bucketName: process.env.S3_BUCKET_NAME,
    },
  },

  // 文章配置
  article: {
    defaultLanguage: process.env.ARTICLE_LANGUAGE || 'zh-CN',
    minLength: parseInt(process.env.ARTICLE_MIN_LENGTH) || 500,
    maxLength: parseInt(process.env.ARTICLE_MAX_LENGTH) || 2000,
    includeImages: process.env.INCLUDE_IMAGES === 'true',
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || 'logs/app.log',
  },
};

// 验证必需的配置
const validateConfig = () => {
  const defaultService = process.env.DEFAULT_AI_SERVICE || 'doubao';
  const required = [];

  // 根据默认AI服务检查对应的API密钥
  if (defaultService === 'doubao') {
    required.push('DOUBAO_API_KEY');
  } else if (defaultService === 'gpt4o') {
    required.push('GPT4O_API_KEY');
  }

  const missing = required.filter((key) => !process.env[key]);

  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables for ${defaultService}: ${missing.join(
        ', '
      )}`
    );
  }

  console.log(`✅ 配置验证通过，默认AI服务: ${defaultService}`);
  console.log(`🤖 AI服务: ${config.ai[defaultService].model}`);
  console.log(`🌐 服务器: http://${config.server.host}:${config.server.port}`);

  if (config.wordpress.siteUrl) {
    console.log(`📝 WordPress: ${config.wordpress.siteUrl}`);
  } else {
    console.log('⚠️  WordPress未配置，仅支持文章生成');
  }
};

module.exports = { config, validateConfig };
