const axios = require('axios');
const { config } = require('../config/config');
const logger = require('../config/logger');
const { getImagesForArticle } = require('./imageServiceOptimized');

/**
 * 获取AI配置
 */
function getAIConfig(serviceName) {
  const service = serviceName || config.ai.defaultService;

  if (service === 'doubao') {
    return {
      apiUrl: config.ai.doubao.apiUrl,
      apiKey: config.ai.doubao.apiKey,
      model: config.ai.doubao.model,
      maxTokens: config.ai.maxTokens,
      temperature: config.ai.temperature,
    };
  } else if (service === 'gpt4o') {
    return {
      apiUrl: config.ai.gpt4o.apiUrl,
      apiKey: config.ai.gpt4o.apiKey,
      model: config.ai.gpt4o.model,
      maxTokens: config.ai.maxTokens,
      temperature: config.ai.temperature,
    };
  } else if (service === 'deepseek') {
    return {
      apiUrl: config.ai.deepseek.apiUrl,
      apiKey: config.ai.deepseek.apiKey,
      model: config.ai.deepseek.model,
      maxTokens: config.ai.maxTokens,
      temperature: config.ai.temperature,
    };
  } else {
    throw new Error(`不支持的AI服务: ${service}`);
  }
}

/**
 * 解析分段响应格式
 * @param {string} content - AI返回的文本内容
 * @returns {Object} 解析后的文章数据
 */
function parseSegmentedResponse(content) {
  try {
    const result = {};
    const lines = content.split('\n');

    logger.info(`🔍 开始解析AI响应，总行数: ${lines.length}`);

    let currentSection = '';
    let currentContent = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // 详细调试：显示每一行
      if (
        trimmedLine.startsWith('===') ||
        trimmedLine.includes('IMAGES') ||
        trimmedLine.includes('Type:')
      ) {
        logger.info(`🔍 行 ${i + 1}: "${line}"`);
      }

      // 识别段落标记（处理可能的额外空格）
      if (trimmedLine.startsWith('===') && trimmedLine.includes('===')) {
        // 保存上一个段落
        if (currentSection && currentContent.length > 0) {
          const sectionContent = currentContent.join('\n').trim();

          logger.info(
            `🔍 处理段落: ${currentSection}, 内容长度: ${sectionContent.length}`
          );

          if (currentSection === 'IMAGES') {
            logger.info('🔍 找到IMAGES段落，开始解析图片数据');
            logger.info('🔍 IMAGES段落内容:', sectionContent.substring(0, 500));
            // 解析图片数组
            result.images = [];
            const imageBlocks = sectionContent
              .split('---')
              .filter((block) => block.trim());

            for (let i = 0; i < imageBlocks.length; i++) {
              const block = imageBlocks[i];
              const imageData = {};
              const imageLines = block.trim().split('\n');

              logger.info(`🔍 解析图片块 ${i + 1}:`, {
                原始块: block.trim().substring(0, 200),
                行数: imageLines.length,
              });

              for (const imageLine of imageLines) {
                const [key, ...valueParts] = imageLine.split(':');
                if (key && valueParts.length > 0) {
                  const value = valueParts.join(':').trim();
                  logger.info(`🔍 解析图片字段: ${key.trim()} = ${value}`);

                  switch (key.trim().toLowerCase()) {
                    case 'description':
                      imageData.description = value;
                      break;
                    case 'alt':
                      imageData.alt = value;
                      break;
                    case 'position':
                      imageData.position = value;
                      break;
                    case 'type':
                      imageData.type = value.toLowerCase(); // technical 或 general
                      logger.info(`✅ 设置图片类型: ${imageData.type}`);
                      break;
                  }
                }
              }

              logger.info(`🔍 解析后的图片数据 ${i + 1}:`, imageData);
              if (imageData.description) {
                result.images.push(imageData);
              } else {
                logger.warn(`⚠️ 图片块 ${i + 1} 缺少描述，跳过`);
              }
            }
          } else {
            // 普通字段 - 应用SEO字段名称映射
            const fieldName = currentSection.toLowerCase();
            const fieldMapping = {
              seotitle: 'seoTitle',
              metadescription: 'metaDescription',
              focuskeyword: 'focusKeyword',
            };

            const mappedFieldName = fieldMapping[fieldName] || fieldName;

            // 调试日志：显示字段名映射
            if (fieldMapping[fieldName]) {
              logger.info(
                `🔄 SEO字段映射(循环): ${fieldName} → ${mappedFieldName}`
              );
            }

            result[mappedFieldName] = sectionContent;
          }
        }

        // 开始新段落，提取段落名称并去除所有空格
        currentSection = trimmedLine.replace(/=/g, '').trim();
        logger.info(`🔍 识别新段落: "${currentSection}"`);
        currentContent = [];
      } else {
        // 保留所有行，包括空行，以正确处理IMAGES段落格式
        currentContent.push(line);
      }
    }

    // 处理最后一个段落（使用相同的逻辑）
    if (currentSection && currentContent.length > 0) {
      const sectionContent = currentContent.join('\n').trim();

      logger.info(
        `🔍 处理最后段落: ${currentSection}, 内容长度: ${sectionContent.length}`
      );

      if (currentSection === 'IMAGES') {
        logger.info('🔍 找到最后的IMAGES段落，开始解析图片数据');
        logger.info('🔍 IMAGES段落内容:', sectionContent.substring(0, 500));
        // 解析图片数组
        result.images = [];
        const imageBlocks = sectionContent
          .split('---')
          .filter((block) => block.trim());

        for (let i = 0; i < imageBlocks.length; i++) {
          const block = imageBlocks[i];
          const imageData = {};
          const imageLines = block.trim().split('\n');

          logger.info(`🔍 解析最后图片块 ${i + 1}:`, {
            原始块: block.trim().substring(0, 200),
            行数: imageLines.length,
          });

          for (const imageLine of imageLines) {
            const [key, ...valueParts] = imageLine.split(':');
            if (key && valueParts.length > 0) {
              const value = valueParts.join(':').trim();
              logger.info(`🔍 解析最后图片字段: ${key.trim()} = ${value}`);

              switch (key.trim().toLowerCase()) {
                case 'description':
                  imageData.description = value;
                  break;
                case 'alt':
                  imageData.alt = value;
                  break;
                case 'position':
                  imageData.position = value;
                  break;
                case 'type':
                  imageData.type = value.toLowerCase(); // technical 或 general
                  logger.info(`✅ 设置最后图片类型: ${imageData.type}`);
                  break;
              }
            }
          }

          logger.info(`🔍 解析后的最后图片数据 ${i + 1}:`, imageData);
          if (imageData.description) {
            result.images.push(imageData);
          } else {
            logger.warn(`⚠️ 最后图片块 ${i + 1} 缺少描述，跳过`);
          }
        }
      } else {
        // SEO字段名称映射：确保与控制器期望的字段名一致
        const fieldName = currentSection.toLowerCase();
        const fieldMapping = {
          seotitle: 'seoTitle',
          metadescription: 'metaDescription',
          focuskeyword: 'focusKeyword',
        };

        const mappedFieldName = fieldMapping[fieldName] || fieldName;

        // 调试日志：显示字段名映射
        if (fieldMapping[fieldName]) {
          logger.info(`🔄 SEO字段映射: ${fieldName} → ${mappedFieldName}`);
        }

        result[mappedFieldName] = sectionContent;
      }
    }

    // 处理categories和tags
    if (result.categories && typeof result.categories === 'string') {
      result.categories = result.categories
        .split(',')
        .map((cat) => cat.trim())
        .filter((cat) => cat);
    }

    if (result.tags && typeof result.tags === 'string') {
      result.tags = result.tags
        .split(',')
        .map((tag) => tag.trim())
        .filter((tag) => tag);
    }

    // 确保必要字段存在
    result.images = result.images || [];
    result.categories = result.categories || [];
    result.tags = result.tags || [];

    logger.info('分段解析成功', {
      解析字段: Object.keys(result),
      图片数量: result.images.length,
      分类数量: result.categories.length,
      标签数量: result.tags.length,
    });

    return result;
  } catch (error) {
    logger.error('分段解析失败', { error: error.message });
    throw new Error(`分段格式解析失败: ${error.message}`);
  }
}

/**
 * 生成文章（分段文本格式）
 * @param {string} keyword - 关键词
 * @param {Object} options - 生成选项
 * @returns {Promise<Object>} 生成的文章数据
 */
async function generateArticle(keyword, options = {}) {
  try {
    const {
      language = config.article.defaultLanguage,
      tone = '专业',
      minLength = config.article.minLength,
      maxLength = config.article.maxLength,
      includeHeadings = true,
      imageOptions = { enabled: false },
      aiService = config.ai.defaultService,
    } = options;

    logger.info(`开始生成文章，关键词: ${keyword}，AI服务: ${aiService}`);

    // 获取对应AI服务的配置
    const aiConfig = getAIConfig(aiService);

    // 优先使用前端传递的图片选项
    const includeImages =
      imageOptions.enabled !== undefined
        ? imageOptions.enabled
        : config.article.includeImages;

    logger.info(
      `图片配置: includeImages=${includeImages}, method=${imageOptions.method}`
    );

    // 根据图片获取方式调整描述要求
    let imageInstruction = '不需要图片';
    if (includeImages) {
      if (imageOptions.method === 'search') {
        imageInstruction =
          '需要1-2张相关图片，在合适位置使用 {{IMAGE:简短英文关键词}} 占位符。重要：如果是抽象概念（数学、科学、技术），使用几何、图表、插图等专业术语，避免自然界生物（如sunflower, flower, nature）';
      } else {
        imageInstruction =
          '需要1-2张与文章内容相关的图片，在合适位置使用 {{IMAGE:详细英文描述}} 占位符。重要：如果是抽象概念（数学、科学、技术），避免自然界生物（如sunflower, flower, nature）';
      }
    }

    const prompt = `你是专业的内容创作者，请根据关键词"${keyword}"撰写一篇原创文章。

文章要求：
- 语言：英文（English）
- 长度：${minLength}-${maxLength}字
- 风格：${tone}
- 结构：${includeHeadings ? '包含清晰的标题和小标题结构' : '流畅的段落结构'}
- 图片：${imageInstruction}
- SEO优化要求：
  * 标题必须以关键词"${keyword}"开头
  * 第一段前50字内必须包含完整关键词"${keyword}"
  * 至少2个H2标题包含关键词或同义词
  * 元描述120-155字符，包含关键词

**重要：请使用以下分段文本格式返回，不要使用JSON：**

===TITLE===
${keyword}: 具体标题内容（以关键词开头，60字符以内）

===SEOTITLE===
完整的SEO标题（50-60字符）

===EXCERPT===
文章摘要，不超过150字，必须包含关键词"${keyword}"

===METADESCRIPTION===
SEO元描述，120-155字符，必须包含完整关键词"${keyword}"，建议在前30字符内出现

===FOCUSKEYWORD===
${keyword}

===SLUG===
英文URL格式，如：golden-ratio-mathematical-guide（必须包含关键词的英文翻译）

===CATEGORIES===
分类1, 分类2, 分类3

===TAGS===
标签1, 标签2, 标签3, 标签4, 标签5

===CONTENT===
<p>第一段必须在第一句包含完整关键词"${keyword}"。${
      includeImages
        ? '必须在合适位置插入图片占位符，每张图片都要有对应的{{IMAGE:...}}占位符。'
        : ''
    }文中包含2-3个内部链接和1-2个外部权威链接。至少2个H2小标题必须包含关键词"${keyword}"。</p>

<h2>${keyword}相关小标题1</h2>
<p>段落内容...</p>

${
  includeImages
    ? '{{IMAGE:' +
      (imageOptions.method === 'search'
        ? '第一张图片的简短英文关键词'
        : '第一张图片的详细英文描述') +
      '}}'
    : ''
}

<h2>包含${keyword}的小标题2</h2>
<p>段落内容...</p>

${
  includeImages
    ? '{{IMAGE:' +
      (imageOptions.method === 'search'
        ? '第二张图片的简短英文关键词'
        : '第二张图片的详细英文描述') +
      '}}'
    : ''
}

<p>后续内容...</p>

${
  includeImages
    ? `===IMAGES===
Description: 详细描述图片的内容，避免提及任何标注、文字、数字或符号
Alt: 包含关键词"${keyword}"的图片alt文本
Position: 在文章中的位置说明
Type: technical 或 general（根据图片内容判断：technical用于学术/科技/数学等专业内容，general用于日常生活内容）

---

Description: 详细描述第二张图片的内容，避免提及任何标注、文字、数字或符号
Alt: 包含关键词"${keyword}"的图片alt文本
Position: 在文章中的位置说明
Type: technical 或 general（判断此图片的内容类型）`
    : ''
}

严格要求：
1. 必须使用上述分段格式，不要使用JSON
2. 每个段落用 ===段落名=== 标记
3. 图片信息用 --- 分隔
4. ${
      includeImages
        ? '图片描述要' +
          (imageOptions.method === 'search'
            ? '简洁具体，对于抽象概念使用专业术语'
            : '清晰详细，专注纯视觉概念展示，严禁提及任何标注、标签、文字、数字、符号')
        : ''
    }
5. content使用HTML格式
6. ${
      includeImages
        ? '关键要求：CONTENT段落中的{{IMAGE:...}}占位符数量必须与IMAGES段落中的图片数量完全一致！每张图片都必须有对应的{{IMAGE:...}}占位符，不能遗漏任何一张图片'
        : ''
    }
7. ${
      includeImages
        ? '{{IMAGE:...}}占位符要放在文章的恰当位置，与图片的Position说明对应'
        : ''
    }

8. ${
      includeImages
        ? '🚫 图片内容禁止：人物、动物、风景、食物等生活化内容。'
        : ''
    }`;

    const requestData = {
      model: aiConfig.model,
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
      max_tokens: aiConfig.maxTokens,
      temperature: aiConfig.temperature,
    };

    const response = await axios.post(aiConfig.apiUrl, requestData, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${aiConfig.apiKey}`,
      },
      timeout: 60000,
    });

    const aiContent = response.data.choices[0].message.content;
    logger.info('AI文章生成成功');
    logger.info(`AI返回的原始内容长度: ${aiContent.length}`);
    logger.info(`AI返回内容前500字符: ${aiContent.substring(0, 500)}`);

    // 💾 保存完整的AI原始响应用于问题诊断
    const timestamp = Date.now();
    const fs = require('fs');
    const path = require('path');
    const debugDir = path.join(__dirname, '../../logs/ai_debug');

    // 确保调试目录存在
    if (!fs.existsSync(debugDir)) {
      fs.mkdirSync(debugDir, { recursive: true });
    }

    const debugFile = path.join(
      debugDir,
      `ai_response_${keyword.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}.txt`
    );
    fs.writeFileSync(
      debugFile,
      `关键词: ${keyword}\n时间: ${new Date().toISOString()}\n原始AI响应:\n${aiContent}`
    );
    logger.info('💾 AI原始响应已保存:', debugFile);

    // 解析分段格式
    const result = parseSegmentedResponse(aiContent);

    // 处理图片
    if (includeImages && result.images && result.images.length > 0) {
      logger.info(`🖼️ 开始处理 ${result.images.length} 张图片`);

      const imageDescriptions = result.images;
      logger.info('图片描述列表', { imageDescriptions });

      // 准备文章上下文信息
      const articleContext = {
        title: result.title,
        content: result.content,
        focusKeyword: result.focuskeyword || keyword,
      };

      const processedImages = await getImagesForArticle(
        imageDescriptions,
        {
          enabled: true,
          articleKeyword: keyword,
          method: imageOptions.method || 'search',
          aiService: imageOptions.aiService,
          searchService: imageOptions.searchService,
        },
        articleContext
      );

      logger.info('图片处理结果', {
        处理前数量: result.images.length,
        处理后数量: processedImages.length,
      });

      result.images = processedImages;

      // 替换内容中的图片占位符
      if (result.content && result.images.length > 0) {
        logger.info('🖼️ 开始替换文章内容中的图片占位符');

        // 先检查文章中有多少个占位符
        const placeholders = result.content.match(/{{IMAGE:[^}]*}}/gi) || [];
        logger.info(
          `📍 问题诊断 - 占位符数量: ${placeholders.length}, 图片数量: ${result.images.length}`
        );
        logger.info(`📍 占位符详情: ${JSON.stringify(placeholders)}`);

        let updatedContent = result.content;
        let replacedCount = 0;

        result.images.forEach((image, index) => {
          const imageHtml = `<figure class="wp-block-image">
            <img src="${image.url}" alt="${image.alt}" title="${image.description}" style="max-width: 100%; height: auto;" />
            <figcaption>${image.description}</figcaption>
          </figure>`;

          const placeholderRegex = new RegExp(`{{IMAGE:[^}]*}}`, 'i');

          if (placeholderRegex.test(updatedContent)) {
            updatedContent = updatedContent.replace(
              placeholderRegex,
              imageHtml
            );
            replacedCount++;
            logger.info(`✅ 第 ${index + 1} 张图片占位符替换成功`);
          } else {
            logger.warn(`⚠️ 第 ${index + 1} 张图片未找到对应占位符`);
          }
        });

        result.content = updatedContent;
        logger.info(
          `🎯 图片占位符替换完成: ${replacedCount}/${result.images.length}`
        );
      }
    }

    logger.info('文章生成完成', {
      标题: result.title,
      内容长度: result.content?.length || 0,
      图片数量: result.images?.length || 0,
    });

    return result;
  } catch (error) {
    logger.error('文章生成失败', { error: error.message });
    throw new Error(`文章生成失败: ${error.message}`);
  }
}

module.exports = {
  generateArticle,
  parseSegmentedResponse,
};
