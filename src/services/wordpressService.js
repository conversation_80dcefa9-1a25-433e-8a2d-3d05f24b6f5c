const axios = require('axios');
const logger = require('../config/logger');
const { config } = require('../config/config');

// 创建WordPress API客户端
const createWordPressClient = () => {
  const { siteUrl, username, password, applicationPassword } = config.wordpress;

  // 记录WordPress配置状态到日志
  logger.info('WordPress客户端初始化', {
    siteUrl: siteUrl ? '已配置' : '未配置',
    username: username ? '已配置' : '未配置',
    authMethod: applicationPassword
      ? '应用密码'
      : password
      ? '用户密码'
      : '未配置',
  });

  // 构建基础URL
  const baseURL = `${siteUrl}/wp-json/wp/v2`;

  // 创建axios实例
  const client = axios.create({
    baseURL,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // 设置认证
  const authString = `${username}:${applicationPassword}`;
  const authHeader = `Basic ${Buffer.from(authString).toString('base64')}`;
  client.defaults.headers.common['Authorization'] = authHeader;

  return client;
};

/**
 * 发布文章到WordPress
 * @param {Object} article - 文章对象
 * @param {string} article.title - 文章标题
 * @param {string} article.content - 文章内容（Markdown格式）
 * @param {string} article.excerpt - 文章摘要
 * @param {Array<string>} article.categories - 文章分类名称数组
 * @param {Array<string>} article.tags - 文章标签数组
 * @param {string} article.status - 发布状态 (publish, draft, pending, private)
 * @returns {Promise<Object>} 发布结果
 */
async function publishArticle(article) {
  try {
    console.log('开始发布文章:', article.title);
    console.log('WordPress配置:', {
      siteUrl: config.wordpress.siteUrl,
      username: config.wordpress.username,
      hasPassword: !!config.wordpress.password,
      hasAppPassword: !!config.wordpress.applicationPassword,
    });

    const client = createWordPressClient();
    logger.info(`准备发布文章到WordPress: ${article.title}`);

    // 1. 处理分类
    const categories = await handleCategories(client, article.categories);

    // 2. 处理标签
    const tags = await handleTags(client, article.tags);

    // 3. 发布文章
    const postData = {
      title: article.title,
      content: article.content,
      excerpt: article.excerpt,
      status: article.status || 'draft', // 默认为草稿
      categories: categories,
      tags: tags,
      slug: article.slug, // 添加URL slug
    };

    // 调试日志：检查slug是否正确传递
    logger.info(`准备发布文章，slug: ${article.slug}`, {
      title: article.title,
      slug: article.slug,
      postDataSlug: postData.slug,
    });

    const response = await client.post('/posts', postData);
    const postId = response.data.id;

    // 检查WordPress返回的实际slug
    logger.info(`WordPress返回的slug: ${response.data.slug}`, {
      requestSlug: article.slug,
      responseSlug: response.data.slug,
      postId: postId,
    });

    // 如果返回的slug与我们设置的不同，尝试单独更新slug
    if (article.slug && response.data.slug !== article.slug) {
      try {
        logger.info(`尝试单独更新slug: ${article.slug}`);
        await client.post(`/posts/${postId}`, { slug: article.slug });
        logger.info(`✅ Slug更新成功: ${article.slug}`);
      } catch (slugError) {
        logger.warn(`❌ Slug更新失败: ${slugError.message}`);
      }
    }

    logger.info(
      `文章发布成功: ${article.title}, ID: ${postId}, 状态: ${response.data.status}`
    );

    // 设置Yoast SEO元数据（简化版本）
    logger.info(
      `检查SEO数据: metaDescription=${!!article.metaDescription}, focusKeyword=${!!article.focusKeyword}`,
      {
        metaDescription: article.metaDescription,
        focusKeyword: article.focusKeyword,
        title: article.title,
      }
    );

    try {
      if (article.metaDescription || article.focusKeyword) {
        const seoData = {};
        if (article.metaDescription) {
          seoData._yoast_wpseo_metadesc = article.metaDescription.substring(
            0,
            155
          );
        }
        if (article.focusKeyword) {
          seoData._yoast_wpseo_focuskw = article.focusKeyword;
        }
        // 不设置SEO标题，让Yoast SEO使用默认标题
        // 避免使用中文标题作为SEO标题

        // 尝试使用PUT方法更新meta字段
        // 使用插件提供的字段名
        const pluginData = {};

        if (seoData._yoast_wpseo_metadesc) {
          pluginData.yoast_meta_description = seoData._yoast_wpseo_metadesc;
        }
        if (seoData._yoast_wpseo_focuskw) {
          pluginData.yoast_focus_keyword = seoData._yoast_wpseo_focuskw;
        }
        // ✅ 使用专门的SEO标题
        if (article.seoTitle) {
          pluginData.yoast_seo_title = article.seoTitle.substring(0, 60);
        }

        // ❌ slug是WordPress核心字段，不是插件字段，不应该在这里设置

        logger.info(`使用插件字段设置Yoast SEO数据`, {
          postId,
          pluginData,
          articleSeoTitle: article.seoTitle,
          articleMetaDescription: article.metaDescription,
          articleFocusKeyword: article.focusKeyword,
        });

        try {
          await client.put(`/posts/${postId}`, pluginData);
          logger.info(`✅ 插件字段设置成功`, { postId, pluginData });
        } catch (pluginError) {
          logger.error(`❌ 插件字段设置失败`, {
            postId,
            pluginData,
            error: pluginError.message,
            response: pluginError.response?.data,
          });
        }

        // 验证设置是否成功
        try {
          const verifyResponse = await client.get(`/posts/${postId}`);
          const actualMeta = verifyResponse.data.meta || {};
          logger.info(`验证SEO设置结果:`, {
            postId,
            设置的数据: seoData,
            实际保存的数据: {
              _yoast_wpseo_metadesc: actualMeta._yoast_wpseo_metadesc,
              _yoast_wpseo_focuskw: actualMeta._yoast_wpseo_focuskw,
              _yoast_wpseo_title: actualMeta._yoast_wpseo_title,
            },
          });
        } catch (verifyError) {
          logger.warn(`验证SEO设置失败: ${verifyError.message}`, { postId });
        }
      } else {
        logger.info(`跳过SEO设置: 没有metaDescription或focusKeyword`, {
          postId,
        });
      }
    } catch (seoError) {
      logger.warn(`Yoast SEO元数据设置失败: ${seoError.message}`, { postId });
      // 不影响主要发布流程，继续执行
    }

    return {
      success: true,
      postId: response.data.id,
      postUrl: response.data.link,
      status: response.data.status,
    };
  } catch (error) {
    logger.error(`文章发布失败: ${error.message}`, {
      error,
      title: article.title,
    });
    throw new Error(`文章发布失败: ${error.message}`);
  }
}

/**
 * 处理文章分类，确保分类存在
 * @param {Object} client - WordPress API客户端
 * @param {Array<string>} categoryNames - 分类名称数组
 * @returns {Promise<Array<number>>} 分类ID数组
 */
async function handleCategories(client, categoryNames = []) {
  if (!categoryNames || categoryNames.length === 0) {
    return [1]; // 默认分类（未分类）
  }

  try {
    // 获取所有现有分类
    const response = await client.get('/categories', { per_page: 100 });
    const existingCategories = response.data;

    const categoryIds = [];

    // 处理每个分类
    for (const name of categoryNames) {
      // 查找现有分类
      const existing = existingCategories.find(
        (cat) => cat.name.toLowerCase() === name.toLowerCase()
      );

      if (existing) {
        // 使用现有分类
        categoryIds.push(existing.id);
      } else {
        // 创建新分类
        try {
          const newCategory = await client.post('/categories', { name });
          categoryIds.push(newCategory.data.id);
          logger.info(`创建了新分类: ${name}, ID: ${newCategory.data.id}`);
        } catch (err) {
          logger.warn(`创建分类失败: ${name}, ${err.message}`);
        }
      }
    }

    return categoryIds.length > 0 ? categoryIds : [1]; // 如果没有有效分类，使用默认分类
  } catch (error) {
    logger.error(`处理分类时出错: ${error.message}`, { error });
    return [1]; // 出错时使用默认分类
  }
}

/**
 * 处理文章标签，确保标签存在
 * @param {Object} client - WordPress API客户端
 * @param {Array<string>} tagNames - 标签名称数组
 * @returns {Promise<Array<number>>} 标签ID数组
 */
async function handleTags(client, tagNames = []) {
  if (!tagNames || tagNames.length === 0) {
    return [];
  }

  try {
    // 获取所有现有标签
    const response = await client.get('/tags', { per_page: 100 });
    const existingTags = response.data;

    const tagIds = [];

    // 处理每个标签
    for (const name of tagNames) {
      // 查找现有标签
      const existing = existingTags.find(
        (tag) => tag.name.toLowerCase() === name.toLowerCase()
      );

      if (existing) {
        // 使用现有标签
        tagIds.push(existing.id);
      } else {
        // 创建新标签
        try {
          const newTag = await client.post('/tags', { name });
          tagIds.push(newTag.data.id);
          logger.info(`创建了新标签: ${name}, ID: ${newTag.data.id}`);
        } catch (err) {
          logger.warn(`创建标签失败: ${name}, ${err.message}`);
        }
      }
    }

    return tagIds;
  } catch (error) {
    logger.error(`处理标签时出错: ${error.message}`, { error });
    return []; // 出错时返回空数组
  }
}

/**
 * 获取WordPress站点信息
 * @returns {Promise<Object>} 站点信息
 */
async function getSiteInfo() {
  try {
    const client = createWordPressClient();
    const response = await client.get('/');

    return {
      success: true,
      name: response.data.name,
      description: response.data.description,
      url: response.data.url,
    };
  } catch (error) {
    logger.error(`获取站点信息失败: ${error.message}`, { error });
    throw new Error(`获取站点信息失败: ${error.message}`);
  }
}

module.exports = {
  publishArticle,
  getSiteInfo,
};
