const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const logger = require('../config/logger');
const { config } = require('../config/config');

/**
 * 上传图片到S3（如果没有配置AWS，则保存到本地）
 */
async function uploadImageToS3(imageData, fileName) {
  try {
    // 如果配置了AWS，使用S3上传
    if (config.image.aws.accessKeyId && config.image.aws.secretAccessKey) {
      const AWS = require('aws-sdk');

      const s3 = new AWS.S3({
        accessKeyId: config.image.aws.accessKeyId,
        secretAccessKey: config.image.aws.secretAccessKey,
        region: config.image.aws.region,
      });

      const key = `images/${Date.now()}-${fileName}`;

      const uploadParams = {
        Bucket: config.image.aws.bucketName,
        Key: key,
        Body: imageData,
        ContentType: 'image/jpeg',
        ACL: 'public-read',
      };

      const result = await s3.upload(uploadParams).promise();
      logger.info(`图片上传到S3成功: ${result.Location}`);
      return result.Location.replace(
        'https://pa-s3-prod.s3.us-west-2.amazonaws.com',
        'https://download-pa-s3.thethinkacademy.com'
      );
    } else {
      // 本地存储备选方案
      const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }

      const uniqueFileName = `${Date.now()}-${uuidv4()}-${fileName}`;
      const filePath = path.join(uploadsDir, uniqueFileName);

      fs.writeFileSync(filePath, imageData);

      const publicUrl = `http://${config.server.host}:${config.server.port}/uploads/${uniqueFileName}`;
      logger.info(`图片保存到本地: ${publicUrl}`);
      return publicUrl;
    }
  } catch (error) {
    logger.error(`图片上传失败: ${error.message}`, { error });
    throw error;
  }
}

module.exports = { uploadImageToS3 };
