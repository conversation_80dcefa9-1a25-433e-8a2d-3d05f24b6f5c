const axios = require('axios');
const { uploadImageToS3 } = require('./s3Service');
const logger = require('../config/logger');
const { config } = require('../config/config');

/**
 * 智能关键词提取器
 * 从文章内容中提取最相关的图片搜索关键词
 */
class KeywordExtractor {
  /**
   * 从图片描述中提取精准的搜索关键词
   * @param {string} description - AI生成的图片描述
   * @param {string} articleKeyword - 文章主关键词
   * @returns {string} 优化后的搜索关键词
   */
  static extractRelevantKeywords(description, articleKeyword) {
    // 简化版本：只移除真正无用的连接词
    const stopWords = [
      'the',
      'and',
      'or',
      'but',
      'in',
      'on',
      'at',
      'to',
      'for',
      'of',
      'with',
      'by',
      'a',
      'an',
      'is',
      'are',
      'was',
      'were',
      'be',
      'been',
      'being',
      'have',
      'has',
      'had',
    ];

    // 清理描述
    let cleanedDescription = description.toLowerCase().trim();

    // 移除停用词但保留专业术语
    stopWords.forEach((word) => {
      cleanedDescription = cleanedDescription.replace(
        new RegExp(`\\b${word}\\b`, 'g'),
        ' '
      );
    });

    // 提取关键词，过滤太短的词
    const words = cleanedDescription
      .replace(/\s+/g, ' ')
      .trim()
      .split(' ')
      .filter((word) => word.length > 2);

    // 保留更多语义信息，最多8个词
    const selectedWords = words.slice(0, 8);
    let finalQuery = selectedWords.join(' ');

    // 确保包含主关键词
    const mainKeyword = articleKeyword.toLowerCase();
    if (!finalQuery.includes(mainKeyword)) {
      finalQuery = `${mainKeyword} ${finalQuery}`.trim();
    }

    logger.info(`关键词提取: "${description}" -> "${finalQuery}"`);
    return finalQuery.trim();
  }

  /**
   * 为特定服务优化关键词 - 保留核心语义
   * @param {string} baseKeywords - 基础关键词
   * @param {string} service - 图片服务名称
   * @returns {string} 优化后的关键词
   */
  static optimizeForService(baseKeywords, service) {
    switch (service) {
      case 'unsplash':
        // Unsplash适合专业摄影和概念图片，保持原有关键词
        return baseKeywords;

      case 'pexels':
        // Pexels适合商业和真实场景，但保留核心概念
        // 只对抽象概念添加实用性修饰，不删除核心词汇
        if (baseKeywords.includes('abstract')) {
          return baseKeywords.replace('abstract', 'professional visual');
        }
        return baseKeywords;

      case 'pixabay':
        // Pixabay范围广泛，保持原样
        return baseKeywords;

      default:
        return baseKeywords;
    }
  }
}

/**
 * 高质量AI图片生成器
 */
class ProfessionalAIGenerator {
  /**
   * 使用Stability AI生成高质量图片
   */
  static async generateWithStability(description, options = {}) {
    if (!config.image.apiKeys.stability) {
      throw new Error(
        '未配置Stability API Key - 请配置STABILITY_API_KEY环境变量'
      );
    }

    let { width = 1024, height = 1024, imageType = 'general' } = options;

    // 确保尺寸符合Stability要求
    width = Math.round(width / 64) * 64;
    height = Math.round(height / 64) * 64;
    width = Math.max(width, 512);
    height = Math.max(height, 512);

    // 专业级prompt优化 - 使用AI提供的图片类型
    const professionalPrompt = this.createProfessionalPrompt(
      description,
      imageType
    );

    logger.info(`Stability AI生成图片: ${width}x${height}`, {
      prompt: professionalPrompt,
    });

    try {
      const response = await axios.post(
        'https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image',
        {
          text_prompts: [
            { text: professionalPrompt, weight: 1 },
            {
              text: 'blurry, low quality, watermark, text, logo, signature',
              weight: -1,
            },
          ],
          cfg_scale: 8, // 更高的引导强度
          height: height,
          width: width,
          samples: 1,
          steps: 40, // 更多步骤提高质量
          style_preset: 'photographic',
          seed: Math.floor(Math.random() * 1000000), // 随机种子增加多样性
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${config.image.apiKeys.stability}`,
            Accept: 'application/json',
          },
          timeout: 45000, // 增加超时时间
        }
      );

      const imageBase64 = response.data.artifacts[0].base64;
      const imageBuffer = Buffer.from(imageBase64, 'base64');

      return {
        data: imageBuffer,
        fileName: `stability-pro-${this.sanitizeFileName(
          description
        )}-${Date.now()}.png`,
        source: 'stability-ai-professional',
        cost: 0.04,
        metadata: {
          prompt: professionalPrompt,
          dimensions: `${width}x${height}`,
          steps: 40,
        },
      };
    } catch (error) {
      const errorMsg = error.response?.data?.message || error.message;
      logger.error(`Stability AI生成失败: ${errorMsg}`);
      throw new Error(`Stability AI生成失败: ${errorMsg}`);
    }
  }

  /**
   * TAL图片生成 - 使用好未来的文生图API
   */
  static async generateWithTAL(description, options = {}) {
    const { config } = require('../config/config');

    if (!config.image.apiKeys.tal) {
      throw new Error('未配置TAL API Key - 请配置TAL_API_KEY环境变量');
    }

    let { width = 1024, height = 1024, imageType = 'general' } = options;

    // 专业级prompt优化
    const professionalPrompt = this.createProfessionalPrompt(
      description,
      imageType
    );

    try {
      logger.info('TAL AI生成图片', {
        size: `${width}x${height}`,
        prompt: professionalPrompt.substring(0, 100) + '...',
      });

      const axios = require('axios');

      const response = await axios.post(
        config.image.tal.apiUrl,
        {
          model: config.image.tal.model,
          prompt: professionalPrompt,
          response_format: 'b64_json',
          size: `${width}x${height}`,
          seed: Math.floor(Math.random() * 1000000), // 随机种子
          extra_body: {
            guidance_scale: config.image.tal.guidanceScale,
            watermark: config.image.tal.watermark,
          },
        },
        {
          headers: {
            'api-key': config.image.apiKeys.tal,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.data?.data?.[0]?.b64_json) {
        throw new Error('TAL API返回格式错误');
      }

      // 处理base64图片数据
      const base64Data = response.data.data[0].b64_json;
      const imageBuffer = Buffer.from(base64Data, 'base64');

      // 生成文件名
      const timestamp = Date.now();
      const shortPrompt = description
        .substring(0, 30)
        .replace(/[^a-zA-Z0-9\s]/g, '');
      const fileName = `tal-ai-${shortPrompt.replace(
        /\s+/g,
        '-'
      )}-${timestamp}.png`;

      logger.info('✅ TAL图片生成成功', {
        fileName,
        imageSize: `${Math.round(imageBuffer.length / 1024)}KB`,
        model: config.image.tal.model,
        cost: 0.03,
      });

      return {
        data: imageBuffer,
        fileName,
        source: 'tal-ai-professional',
        cost: 0.03, // TAL服务的估算成本
        metadata: {
          prompt: professionalPrompt,
          dimensions: `${width}x${height}`,
          model: config.image.tal.model,
        },
      };
    } catch (error) {
      const errorMsg = error.response?.data?.error?.message || error.message;
      logger.error(`TAL AI生成失败: ${errorMsg}`);
      throw new Error(`TAL AI生成失败: ${errorMsg}`);
    }
  }

  /**
   * 创建专业级图片生成prompt - 针对知识类文章优化
   */
  static createProfessionalPrompt(description, imageType = 'general') {
    // 直接使用AI生成文章时提供的图片类型
    const isTechnical = imageType === 'technical';

    if (isTechnical) {
      // 技术类教育图表：保持教育性但用视觉编码
      return `Technical educational diagram. ${description}.`;
    } else {
      // 概念类教育图表：保持教育性但用视觉编码
      return `Educational diagram with visual-only elements. ${description}.`;
    }
  }

  /**
   * 用AI智能判断增强图片描述 - 让AI自己分析学科类型
   */
  static enhanceDescriptionWithContext(
    originalDescription,
    contextualInfo,
    articleKeyword
  ) {
    // 简化版本：让AI自己判断，只提供基本的学术要求
    let enhancedDescription = originalDescription;

    // 添加学科知识专用要求 - 强调极简风格
    enhancedDescription +=
      ', ultra-minimalist academic style, single focused element only, clean white background, maximum 3 visual elements, geometric simplicity, textbook-quality illustration';

    // 确保与主关键词相关
    if (
      articleKeyword &&
      !enhancedDescription.toLowerCase().includes(articleKeyword.toLowerCase())
    ) {
      enhancedDescription += `, specifically about ${articleKeyword}`;
    }

    // 让AI自己判断适合的风格 - 强调简洁
    enhancedDescription +=
      ', AI should create the simplest possible visualization with minimal elements, extreme simplicity required';

    logger.info(
      `📝 描述增强: "${originalDescription}" -> "${enhancedDescription}"`
    );

    return enhancedDescription;
  }

  static sanitizeFileName(text) {
    return text.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-').substring(0, 30);
  }
}

/**
 * 优化的图片搜索服务
 */
class OptimizedImageSearch {
  /**
   * Unsplash搜索 - 高质量专业摄影
   */
  static async searchUnsplash(description, articleKeyword) {
    if (!config.image.apiKeys.unsplash) {
      throw new Error(
        '未配置Unsplash API Key - 请配置UNSPLASH_ACCESS_KEY环境变量'
      );
    }

    const optimizedKeywords = KeywordExtractor.extractRelevantKeywords(
      description,
      articleKeyword
    );
    const searchQuery = KeywordExtractor.optimizeForService(
      optimizedKeywords,
      'unsplash'
    );

    logger.info(`Unsplash搜索: "${searchQuery}" (原描述: ${description})`);

    try {
      const response = await axios.get(
        'https://api.unsplash.com/search/photos',
        {
          params: {
            query: searchQuery,
            per_page: 20, // 增加候选数量
            orientation: 'landscape',
            content_filter: 'high',
            order_by: 'relevant', // 按相关性排序
            color: undefined, // 不限制颜色
          },
          headers: {
            Authorization: `Client-ID ${config.image.apiKeys.unsplash}`,
          },
          timeout: 15000,
        }
      );

      if (response.data.results.length === 0) {
        throw new Error(`Unsplash未找到相关图片，搜索词: "${searchQuery}"`);
      }

      // 选择高质量图片（基于点赞数和下载数）
      const sortedResults = response.data.results
        .filter((photo) => photo.likes > 10) // 过滤低质量图片
        .sort((a, b) => b.likes + b.downloads - (a.likes + a.downloads));

      if (sortedResults.length === 0) {
        throw new Error('Unsplash未找到高质量相关图片');
      }

      const selectedPhoto = sortedResults[0]; // 选择最受欢迎的
      const imageUrl = selectedPhoto.urls.regular;

      const imageResponse = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
        timeout: 15000,
      });

      return {
        data: imageResponse.data,
        fileName: `unsplash-${this.sanitizeFileName(
          searchQuery
        )}-${Date.now()}.jpg`,
        source: 'unsplash-optimized',
        cost: 0,
        metadata: {
          photographer: selectedPhoto.user.name,
          description:
            selectedPhoto.description || selectedPhoto.alt_description,
          likes: selectedPhoto.likes,
          downloads: selectedPhoto.downloads,
          searchQuery: searchQuery,
        },
      };
    } catch (error) {
      const errorMsg = error.response?.data?.errors?.[0] || error.message;
      logger.error(`Unsplash搜索失败: ${errorMsg}`);
      throw new Error(`Unsplash搜索失败: ${errorMsg}`);
    }
  }

  /**
   * Pexels搜索 - 商业摄影
   */
  static async searchPexels(description, articleKeyword) {
    if (!config.image.apiKeys.pexels) {
      throw new Error('未配置Pexels API Key - 请配置PEXELS_API_KEY环境变量');
    }

    const optimizedKeywords = KeywordExtractor.extractRelevantKeywords(
      description,
      articleKeyword
    );
    const searchQuery = KeywordExtractor.optimizeForService(
      optimizedKeywords,
      'pexels'
    );

    logger.info(`Pexels搜索: "${searchQuery}" (原描述: ${description})`);

    try {
      const response = await axios.get('https://api.pexels.com/v1/search', {
        params: {
          query: searchQuery,
          per_page: 15,
          orientation: 'landscape',
          size: 'large',
        },
        headers: {
          Authorization: config.image.apiKeys.pexels,
        },
        timeout: 15000,
      });

      if (response.data.photos.length === 0) {
        throw new Error(`Pexels未找到相关图片，搜索词: "${searchQuery}"`);
      }

      // 选择最高分辨率的图片
      const bestPhoto = response.data.photos.sort(
        (a, b) => b.width * b.height - a.width * a.height
      )[0];

      const imageUrl = bestPhoto.src.large2x || bestPhoto.src.large;

      const imageResponse = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
        timeout: 15000,
      });

      return {
        data: imageResponse.data,
        fileName: `pexels-${this.sanitizeFileName(
          searchQuery
        )}-${Date.now()}.jpg`,
        source: 'pexels-optimized',
        cost: 0,
        metadata: {
          photographer: bestPhoto.photographer,
          resolution: `${bestPhoto.width}x${bestPhoto.height}`,
          searchQuery: searchQuery,
        },
      };
    } catch (error) {
      const errorMsg = error.response?.data?.error || error.message;
      logger.error(`Pexels搜索失败: ${errorMsg}`);
      throw new Error(`Pexels搜索失败: ${errorMsg}`);
    }
  }

  static sanitizeFileName(text) {
    return text.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-').substring(0, 30);
  }
}

/**
 * 主图片控制器 - 无降级方案，严格按指定服务执行
 */
class StrictImageController {
  /**
   * 严格按照指定方式生成图片，失败即报错
   */
  static async generateImage(description, options, imageType = 'general') {
    const { method, aiService, searchService } = options;

    logger.info(
      `严格图片生成: method=${method}, service=${aiService || searchService}`,
      {
        description,
        imageType,
      }
    );

    try {
      if (method === 'ai') {
        return await this.generateWithOptimizedPrompt(
          description,
          options,
          imageType
        );
      } else if (method === 'search') {
        return await this.generateWithSearch(description, options, imageType);
      } else {
        throw new Error(`不支持的图片生成方法: ${method}`);
      }
    } catch (error) {
      // 不进行任何降级，直接抛出错误
      logger.error(`图片生成失败: ${error.message}`, {
        method,
        service: aiService || searchService,
        imageType,
      });
      throw error;
    }
  }

  static async generateWithOptimizedPrompt(
    description,
    options,
    imageType = 'general'
  ) {
    // 首先用AI优化prompt
    const optimizedPrompt = await this.optimizePromptWithAI(
      description,
      imageType
    );
    return await this.generateWithAI(optimizedPrompt, options, imageType);
  }

  static async generateWithAI(description, options, imageType = 'general') {
    const { aiService } = options;

    switch (aiService) {
      case 'stability':
        return await ProfessionalAIGenerator.generateWithStability(
          description,
          { ...options, imageType }
        );
      case 'tal':
        return await ProfessionalAIGenerator.generateWithTAL(description, {
          ...options,
          imageType,
        });
      default:
        throw new Error(`不支持的AI图片服务: ${aiService}`);
    }
  }

  static async generateWithSearch(description, options, imageType = 'general') {
    const { searchService } = options;

    switch (searchService) {
      case 'unsplash':
        return await OptimizedImageSearch.searchUnsplash(
          description,
          '' // 搜索服务不需要imageType，保持原有的articleKeyword参数为空
        );
      case 'pexels':
        return await OptimizedImageSearch.searchPexels(
          description,
          '' // 搜索服务不需要imageType，保持原有的articleKeyword参数为空
        );
      default:
        throw new Error(`不支持的图片搜索服务: ${searchService}`);
    }
  }

  /**
   * 用AI优化文生图prompt
   */
  static async optimizePromptWithAI(description, imageType = 'general') {
    const { config } = require('../config/config');
    const axios = require('axios');

    logger.info('🤖 开始AI优化prompt', {
      原始描述: description,
      图片类型: imageType,
    });

    try {
      const optimizationPrompt = `你是专业的文生图prompt工程师，特别擅长为TAL AI图像生成模型优化提示词。

用户的原始描述：${description}

请将这个描述优化成最适合TAL文生图模型的英文prompt，要求：

1. **专业图表无文字化**：
   - 强调"professional diagram with visual-only elements"
   - 避免"labeled"、"annotated"、"with text"等触发文字的描述


请直接输出优化后的英文prompt，不要解释：`;

      const response = await axios.post(
        config.ai.gpt4o.apiUrl,
        {
          model: config.ai.gpt4o.model,
          messages: [
            {
              role: 'user',
              content: optimizationPrompt,
            },
          ],
          max_tokens: 200,
          temperature: 0.3,
        },
        {
          headers: {
            Authorization: `Bearer ${config.ai.gpt4o.apiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 15000,
        }
      );

      const optimizedPrompt = response.data.choices[0].message.content.trim();

      logger.info('✅ Prompt优化成功', {
        原始描述: description,
        优化后prompt: optimizedPrompt,
      });

      return optimizedPrompt;
    } catch (error) {
      logger.error('❌ Prompt优化失败，使用原始描述', {
        error: error.message,
        原始描述: description,
      });

      // 如果优化失败，返回原始描述（但仍应用基本优化）
      return `Educational diagram about ${description}. Visual-only illustration using color coding and shape differentiation instead of text. Professional educational graphic, clear concept visualization without text, symbols, or annotations, 2D flat design, white background`;
    }
  }
}

/**
 * 为文章获取图片的主函数
 */
async function getImagesForArticle(
  imageDescriptions,
  imageOptions,
  articleContext = {}
) {
  if (
    !imageOptions.enabled ||
    !imageDescriptions ||
    imageDescriptions.length === 0
  ) {
    return [];
  }

  const results = [];
  let totalCost = 0;

  // 不再需要额外提取上下文信息，直接使用AI生成文章时提供的type
  const { title = '', content = '', focusKeyword = '' } = articleContext;

  for (let i = 0; i < imageDescriptions.length; i++) {
    const imageDesc = imageDescriptions[i];
    // 直接使用AI生成的图片描述，无需额外增强
    const description = imageDesc.description;

    logger.info(`🖼️ 处理第 ${i + 1} 张图片: ${description}`, {
      图片序号: `${i + 1}/${imageDescriptions.length}`,
      文章关键词: imageOptions.articleKeyword || '',
      生成方法: imageOptions.method,
      服务提供商: imageOptions.searchService || imageOptions.aiService,
    });

    try {
      const startTime = Date.now();
      const imageResult = await StrictImageController.generateImage(
        description,
        imageOptions,
        imageDesc.type || 'general' // 传递AI判断的图片类型
      );
      const processTime = Date.now() - startTime;

      logger.info(`✅ 图片生成成功: ${imageResult.source}`, {
        cost: imageResult.cost,
        metadata: imageResult.metadata,
        性能指标: {
          处理耗时: `${processTime}ms`,
          图片大小: imageResult.data
            ? `${Math.round(imageResult.data.length / 1024)}KB`
            : '未知',
          文件名: imageResult.fileName,
        },
      });

      // 上传到S3
      logger.info(`☁️ 开始上传图片到S3: ${imageResult.fileName}`);
      const uploadStartTime = Date.now();
      const imageUrl = await uploadImageToS3(
        imageResult.data,
        imageResult.fileName
      );
      const uploadTime = Date.now() - uploadStartTime;

      logger.info(`☁️ S3上传完成`, {
        图片URL: imageUrl,
        上传耗时: `${uploadTime}ms`,
        文件大小: `${Math.round(imageResult.data.length / 1024)}KB`,
      });

      results.push({
        ...imageDesc,
        url: imageUrl,
        source: imageResult.source,
        metadata: imageResult.metadata,
      });

      totalCost += imageResult.cost || 0;

      // 请求间隔
      if (i < imageDescriptions.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }
    } catch (error) {
      logger.error(`❌ 第 ${i + 1} 张图片生成失败: ${error.message}`, {
        description,
        图片序号: `${i + 1}/${imageDescriptions.length}`,
        错误类型: error.name || 'Unknown',
        生成方法: imageOptions.method,
        服务提供商: imageOptions.searchService || imageOptions.aiService,
        处理耗时: '已中断',
      });
      // 按要求，不使用降级方案，直接抛出错误
      throw error;
    }
  }

  logger.info(`🎉 所有图片处理完成`, {
    总图片数量: results.length,
    总成本: `$${totalCost.toFixed(4)}`,
    处理结果: results.map((img, idx) => ({
      序号: idx + 1,
      描述: img.description,
      URL: img.url,
      来源: img.source,
    })),
  });
  return results;
}

module.exports = {
  getImagesForArticle,
  KeywordExtractor,
  StrictImageController,
};
