const express = require('express');
const router = express.Router();
const {
  generateArticleHandler,
  publishArticleHandler,
  generateAndPublishHandler,
  getSiteInfoHandler,
} = require('../controllers/articleController');
// 中间件暂时移除

// 生成文章
router.post('/generate', generateArticleHandler);

// 发布文章到WordPress
router.post('/publish', publishArticleHandler);

// 一键生成并发布文章
router.post('/generate-and-publish', generateAndPublishHandler);

// 获取WordPress站点信息
router.get('/site-info', getSiteInfoHandler);

// 健康检查
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'API服务正常运行',
    timestamp: new Date().toISOString(),
  });
});

module.exports = router;
